import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import math
import requests
import json
from PIL import Image, ImageTk, ImageDraw
import io
import urllib.parse
import threading
from typing import List, Tuple, Optional
import os
from datetime import datetime
import numpy as np

# Try to import OpenCV and other advanced libraries
try:
    import cv2

    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV nie je nainštalované. Niektoré pokročilé funkcie nebudú dostupné.")

try:
    from skimage import filters, segmentation, measure, morphology, color  # Pridané 'color'
    from skimage.feature import canny
    from skimage.future import graph  # Pridané pre RAG

    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False
    print("scikit-image nie je nainštalované. Niektoré pokročilé funkcie nebudú dostupné.")


try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("matplotlib nie je nainštalované. Vizualizácia kontúr nebude dostupná.")

class Point:
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y

class RoofAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Analyzátor rozmerov strechy - Google Maps")
        self.root.geometry("1200x900")
        self.root.configure(bg='#f3f4f6')

        # Application state
        self.points: List[Point] = []
        self.canvas_width = 800
        self.canvas_height = 600
        self.meters_per_pixel = 0.05
        self.current_image = None
        self.photo_image = None
        self.current_lat = None
        self.current_lng = None
        self.current_zoom = 20
        self.map_type = "satellite"

        # Detection and contour data
        self.detected_contours = []
        self.show_contours = tk.BooleanVar(value=True)
        self.show_edges = tk.BooleanVar(value=False)
        self.processed_image = None
        self.contour_overlay = None

        # Multimodal fusion parameters
        self.fusion_weights = {
            'building': 0.3,    # Central building detection
            'shape': 0.25,      # Rectangular shape detection
            'texture': 0.2,     # Texture uniformity
            'shadow': 0.15,     # Shadow-based detection
            'edge': 0.1         # Edge-based features
        }
        self.fusion_threshold = 0.5  # Final threshold for roof classification

        # Smart workflow parameters
        self.roadmap_image = None  # Pre identifikáciu budovy
        self.target_building_bounds = None  # Ohraničenie cieľovej budovy
        self.building_identified = False
        self.workflow_active = False

        # Google Maps API key
        self.api_key = self.load_api_key()

        # Geocoding cache
        self.geocoding_cache = {}

        self.setup_ui()
        self.check_api_key()

    def load_api_key(self):
        """Load Google Maps API key from file or environment"""
        if os.path.exists("api_key.txt"):
            with open("api_key.txt", "r") as f:
                key = f.read().strip()
                if key:
                    return key

        key = os.environ.get("GOOGLE_MAPS_API_KEY")
        if key:
            return key

        return None

    def check_api_key(self):
        """Check if API key is configured"""
        if not self.api_key:
            response = messagebox.askyesno(
                "Google Maps API Key",
                "Google Maps API kľúč nie je nakonfigurovaný.\n\n"
                "Chcete zadať API kľúč teraz?\n\n"
                "Bez API kľúča aplikácia nebude fungovať správne."
            )
            if response:
                self.configure_api_key()

    def configure_api_key(self):
        """Configure Google Maps API key"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Konfigurácia Google Maps API")
        dialog.geometry("500x300")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        tk.Label(dialog, text="Google Maps API Konfigurácia",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)

        tk.Label(dialog, text="Pre použitie tejto aplikácie potrebujete Google Maps API kľúč.",
                font=('Arial', 10), bg='#f3f4f6', wraplength=450).pack(pady=5)

        tk.Label(dialog, text="1. Idite na: https://console.cloud.google.com/",
                font=('Arial', 9), bg='#f3f4f6', fg='blue').pack(pady=2)

        tk.Label(dialog, text="2. Vytvorte nový projekt alebo vyberte existujúci",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)

        tk.Label(dialog, text="3. Povoľte Maps Static API a Geocoding API",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)

        tk.Label(dialog, text="4. Vytvorte API kľúč v sekcii 'Credentials'",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)

        tk.Label(dialog, text="Zadajte váš Google Maps API kľúč:",
                font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(pady=(20, 5))

        api_entry = tk.Entry(dialog, width=60, font=('Arial', 10))
        api_entry.pack(pady=5)

        def save_key():
            key = api_entry.get().strip()
            if key:
                self.api_key = key
                with open("api_key.txt", "w") as f:
                    f.write(key)
                messagebox.showinfo("Úspech", "API kľúč bol uložený!")
                dialog.destroy()
            else:
                messagebox.showwarning("Chyba", "Prosím zadajte platný API kľúč.")

        tk.Button(dialog, text="Uložiť", command=save_key,
                 bg='#2563eb', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5).pack(pady=10)

        tk.Button(dialog, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 11),
                 padx=20, pady=5).pack(pady=5)

    def setup_ui(self):
        main_frame = tk.Frame(self.root, bg='#f3f4f6', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        title_label = tk.Label(main_frame, text="Analyzátor rozmerov strechy",
                              font=('Arial', 24, 'bold'), bg='#f3f4f6', fg='#1f2937')
        title_label.pack(pady=(0, 10))

        subtitle_label = tk.Label(main_frame,
                                 text="Použite 🎯 Smart Workflow pre inteligentný roadmap→satellite prechod s detekciou prvkov strechy.",
                                 font=('Arial', 12), bg='#f3f4f6', fg='#6b7280')
        subtitle_label.pack(pady=(0, 20))

        search_frame = tk.Frame(main_frame, bg='#f3f4f6')
        search_frame.pack(fill=tk.X, pady=(0, 20))

        address_frame = tk.Frame(search_frame, bg='#f3f4f6')
        address_frame.pack(fill=tk.X, pady=(0, 10))

        self.address_var = tk.StringVar()
        self.address_entry = tk.Entry(address_frame, textvariable=self.address_var,
                                     font=('Arial', 12), width=40)
        self.address_entry.pack(side=tk.LEFT, padx=(0, 10), ipady=5)
        self.address_entry.insert(0, "Zadajte adresu (napr. Bratislava, Slovensko)")

        search_btn = tk.Button(address_frame, text="Hľadať", command=self.handle_search,
                              bg='#2563eb', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=5)
        search_btn.pack(side=tk.LEFT, padx=(0, 10))

        smart_btn = tk.Button(address_frame, text="🎯 Smart Workflow", command=self.start_smart_workflow,
                             bg='#7c3aed', fg='white', font=('Arial', 12, 'bold'),
                             padx=20, pady=5)
        smart_btn.pack(side=tk.LEFT)

        controls_frame_map = tk.Frame(search_frame, bg='#f3f4f6') # Renamed to avoid conflict
        controls_frame_map.pack(fill=tk.X)

        tk.Label(controls_frame_map, text="Zoom:", font=('Arial', 10), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 5))

        self.zoom_var = tk.IntVar(value=20)
        zoom_scale = tk.Scale(controls_frame_map, from_=15, to=22, orient=tk.HORIZONTAL,
                             variable=self.zoom_var, command=self.on_zoom_change,
                             bg='#f3f4f6', length=100)
        zoom_scale.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(controls_frame_map, text="Typ mapy:", font=('Arial', 10), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 5))

        self.map_type_var = tk.StringVar(value="satellite")
        map_type_combo = ttk.Combobox(controls_frame_map, textvariable=self.map_type_var,
                                     values=["satellite", "hybrid", "roadmap"],
                                     state="readonly", width=10)
        map_type_combo.pack(side=tk.LEFT, padx=(0, 20))
        map_type_combo.bind('<<ComboboxSelected>>', self.on_map_type_change)

        refresh_btn = tk.Button(controls_frame_map, text="Obnoviť mapu", command=self.refresh_map,
                               bg='#059669', fg='white', font=('Arial', 10, 'bold'),
                               padx=15, pady=3)
        refresh_btn.pack(side=tk.LEFT)

        detection_frame = tk.Frame(search_frame, bg='#f3f4f6')
        detection_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Label(detection_frame, text="Zobrazenie:", font=('Arial', 10, 'bold'), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 10))

        contours_check = tk.Checkbutton(detection_frame, text="Kontúry", variable=self.show_contours,
                                       command=self.toggle_contours, bg='#f3f4f6', font=('Arial', 9))
        contours_check.pack(side=tk.LEFT, padx=(0, 10))

        edges_check = tk.Checkbutton(detection_frame, text="Hrany", variable=self.show_edges,
                                    command=self.toggle_edges, bg='#f3f4f6', font=('Arial', 9))
        edges_check.pack(side=tk.LEFT, padx=(0, 10))

        detect_btn = tk.Button(detection_frame, text="Detekcia striech", command=self.detect_roofs,
                              bg='#7c3aed', fg='white', font=('Arial', 10, 'bold'),
                              padx=15, pady=3)
        detect_btn.pack(side=tk.LEFT, padx=(10, 0))

        contour_btn = tk.Button(detection_frame, text="Zobraziť kontúry", command=self.show_contour_analysis,
                               bg='#0891b2', fg='white', font=('Arial', 10, 'bold'),
                               padx=15, pady=3)
        contour_btn.pack(side=tk.LEFT, padx=(10, 0))

        fine_tune_btn = tk.Button(detection_frame, text="Doladiť detekciu", command=self.show_fine_tune_dialog,
                                 bg='#8b5cf6', fg='white', font=('Arial', 10, 'bold'),
                                 padx=15, pady=3)
        fine_tune_btn.pack(side=tk.LEFT, padx=(10, 0))

        fusion_btn = tk.Button(detection_frame, text="Nastavenia fúzie", command=self.show_fusion_settings,
                              bg='#7c3aed', fg='white', font=('Arial', 10, 'bold'),
                              padx=15, pady=3)
        fusion_btn.pack(side=tk.LEFT, padx=(10, 0))

        canvas_frame = tk.Frame(main_frame, bg='#e5e7eb', relief=tk.RAISED, bd=2)
        canvas_frame.pack(pady=(0, 20))

        self.canvas = tk.Canvas(canvas_frame, width=self.canvas_width, height=self.canvas_height,
                               bg='#e5e7eb', cursor='crosshair')
        self.canvas.pack(padx=10, pady=10)
        self.canvas.bind('<Button-1>', self.handle_canvas_click)

        controls_frame_buttons = tk.Frame(main_frame, bg='#f3f4f6') # Renamed to avoid conflict
        controls_frame_buttons.pack(fill=tk.X)

        left_buttons = tk.Frame(controls_frame_buttons, bg='#f3f4f6')
        left_buttons.pack(side=tk.LEFT)

        self.auto_analyze_btn = tk.Button(left_buttons, text="Automatická Analýza",
                                         command=self.handle_auto_analyze,
                                         bg='#059669', fg='white', font=('Arial', 11, 'bold'),
                                         padx=15, pady=5, state=tk.DISABLED)
        self.auto_analyze_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.undo_btn = tk.Button(left_buttons, text="Späť", command=self.handle_undo,
                                 bg='#eab308', fg='white', font=('Arial', 11, 'bold'),
                                 padx=15, pady=5, state=tk.DISABLED)
        self.undo_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.reset_btn = tk.Button(left_buttons, text="Resetovať", command=self.handle_reset,
                                  bg='#dc2626', fg='white', font=('Arial', 11, 'bold'),
                                  padx=15, pady=5, state=tk.DISABLED)
        self.reset_btn.pack(side=tk.LEFT)

        results_frame = tk.Frame(controls_frame_buttons, bg='#f3f4f6')
        results_frame.pack(side=tk.RIGHT)

        result_label = tk.Label(results_frame, text="Plocha strechy:",
                               font=('Arial', 14, 'bold'), bg='#f3f4f6', fg='#1f2937')
        result_label.pack()

        self.area_label = tk.Label(results_frame, text="0.00 m²",
                                  font=('Arial', 18, 'bold'), bg='#f3f4f6', fg='#2563eb')
        self.area_label.pack()

        self.status_var = tk.StringVar(value="Pripravený")
        status_bar = tk.Label(main_frame, textvariable=self.status_var,
                             font=('Arial', 9), bg='#e5e7eb', fg='#374151',
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, pady=(10, 0))

        self.address_entry.bind('<Return>', lambda e: self.handle_search())

        self.create_menu()

    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Súbor", menu=file_menu)
        file_menu.add_command(label="Exportovať výsledky...", command=self.export_results)
        file_menu.add_command(label="Uložiť projekt...", command=self.save_project)
        file_menu.add_command(label="Načítať projekt...", command=self.load_project)
        file_menu.add_separator()
        file_menu.add_command(label="Ukončiť", command=self.root.quit)

        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Nastavenia", menu=settings_menu)
        settings_menu.add_command(label="API kľúč...", command=self.configure_api_key)

        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Pomoc", menu=help_menu)
        help_menu.add_command(label="O aplikácii", command=self.show_about)

    def on_zoom_change(self, value):
        self.current_zoom = int(value)
        if self.current_lat and self.current_lng:
            self.refresh_map()

    def on_map_type_change(self, event):
        self.map_type = self.map_type_var.get()
        if self.current_lat and self.current_lng:
            self.refresh_map()

    def refresh_map(self):
        if self.current_lat and self.current_lng:
            self.load_satellite_image_from_coords(self.current_lat, self.current_lng)

    def toggle_contours(self):
        self.redraw_canvas()

    def toggle_edges(self):
        if self.show_edges.get():
            if not self.current_image:
                messagebox.showwarning("Upozornenie", "Najprv načítajte mapu pre zobrazenie hrán.")
                self.show_edges.set(False)
                return
            if not hasattr(self, 'edge_overlay') or self.edge_overlay is None:
                self.create_edge_overlay()
        self.redraw_canvas()

    def create_edge_overlay(self):
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv načítajte mapu.")
            self.edge_overlay = None
            return

        if not OPENCV_AVAILABLE:
            messagebox.showerror("Chyba", "OpenCV nie je nainštalované. Detekcia hrán nie je dostupná.")
            self.edge_overlay = None
            return

        try:
            self.status_var.set("Vytváram detekciu hrán...")

            img_array = np.array(self.current_image)

            if img_array.size == 0:
                raise ValueError("Prázdny obrázok")

            if len(img_array.shape) == 3:
                if img_array.shape[2] == 3:
                    gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                elif img_array.shape[2] == 4:
                    rgb_array = img_array[:, :, :3]
                    gray = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2GRAY)
                else:
                    raise ValueError(f"Nepodporovaný počet kanálov: {img_array.shape[2]}")
            elif len(img_array.shape) == 2:
                gray = img_array.astype(np.uint8)
            else:
                raise ValueError(f"Nepodporovaný formát obrázka: {img_array.shape}")

            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            edges = cv2.Canny(blurred, 50, 150)

            h, w = edges.shape
            edge_overlay_arr = np.zeros((h, w, 4), dtype=np.uint8) # Renamed to avoid conflict

            edge_mask = edges > 0
            edge_overlay_arr[edge_mask] = [255, 255, 255, 200]

            self.edge_overlay = Image.fromarray(edge_overlay_arr, 'RGBA')
            self.status_var.set("Detekcia hrán vytvorená")

        except Exception as e:
            error_msg = f"Chyba pri vytváraní detekcie hrán: {str(e)}"
            print(error_msg)
            messagebox.showerror("Chyba detekcie hrán", error_msg)
            self.edge_overlay = None
            self.status_var.set("Chyba pri detekcii hrán")

    def detect_roofs(self):
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv načítajte mapu.")
            return

        if not OPENCV_AVAILABLE:
            messagebox.showerror("Chyba", "OpenCV nie je nainštalované. Nainštalujte ho príkazom: pip install opencv-python")
            return

        self.status_var.set("Detekujem strechy...")
        threading.Thread(target=self.perform_roof_detection, daemon=True).start()

    def show_contour_analysis(self):
        if not self.detected_contours:
            messagebox.showwarning("Upozornenie", "Najprv spustite detekciu striech.")
            return

        if not MATPLOTLIB_AVAILABLE:
            messagebox.showerror("Chyba", "matplotlib nie je nainštalované. Nainštalujte ho príkazom: pip install matplotlib")
            return

        self.create_contour_analysis_window()

    def show_fine_tune_dialog(self):
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv načítajte mapu.")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("Doladenie detekcie striech")
        dialog.geometry("450x350")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        tk.Label(dialog, text="Doladenie parametrov detekcie",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)

        params_frame = tk.Frame(dialog, bg='#f3f4f6')
        params_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        tk.Label(params_frame, text="Minimálna plocha (% z obrazu):",
                font=('Arial', 11), bg='#f3f4f6').pack(anchor='w', pady=(5, 2))

        min_area_var = tk.DoubleVar(value=0.5)
        min_area_scale = tk.Scale(params_frame, from_=0.1, to=2.0, resolution=0.1,
                                 orient=tk.HORIZONTAL, variable=min_area_var,
                                 bg='#f3f4f6', length=250)
        min_area_scale.pack(fill=tk.X, pady=(0, 5))

        tk.Label(params_frame, text="Maximálna plocha (% z obrazu):",
                font=('Arial', 11), bg='#f3f4f6').pack(anchor='w', pady=(5, 2))

        max_area_var = tk.DoubleVar(value=25.0)
        max_area_scale = tk.Scale(params_frame, from_=5.0, to=50.0, resolution=1.0,
                                 orient=tk.HORIZONTAL, variable=max_area_var,
                                 bg='#f3f4f6', length=250)
        max_area_scale.pack(fill=tk.X, pady=(0, 5))

        tk.Label(params_frame, text="Prah skóre budovy:",
                font=('Arial', 11), bg='#f3f4f6').pack(anchor='w', pady=(5, 2))

        threshold_var = tk.DoubleVar(value=0.3)
        threshold_scale = tk.Scale(params_frame, from_=0.1, to=0.8, resolution=0.05,
                                  orient=tk.HORIZONTAL, variable=threshold_var,
                                  bg='#f3f4f6', length=250)
        threshold_scale.pack(fill=tk.X, pady=(0, 10))

        buttons_frame = tk.Frame(dialog, bg='#f3f4f6')
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        def apply_settings():
            self.custom_min_area = min_area_var.get() / 100.0
            self.custom_max_area = max_area_var.get() / 100.0
            self.custom_threshold = threshold_var.get()
            dialog.destroy()
            self.status_var.set("Aplikujem vlastné nastavenia...")
            threading.Thread(target=self.run_custom_detection, daemon=True).start()

        def reset_defaults():
            min_area_var.set(0.5)
            max_area_var.set(25.0)
            threshold_var.set(0.3)

        tk.Button(buttons_frame, text="Aplikovať", command=apply_settings,
                 bg='#059669', fg='white', font=('Arial', 11, 'bold'),
                 padx=15, pady=3).pack(side=tk.LEFT, padx=(0, 5))

        tk.Button(buttons_frame, text="Reset", command=reset_defaults,
                 bg='#eab308', fg='white', font=('Arial', 10),
                 padx=15, pady=3).pack(side=tk.LEFT, padx=(0, 5))

        tk.Button(buttons_frame, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 10),
                 padx=15, pady=3).pack(side=tk.RIGHT)

    def show_fusion_settings(self):
        """Zobrazí dialog pre nastavenie parametrov multimodálnej fúzie."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Nastavenia multimodálnej fúzie")
        dialog.geometry("500x400")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        tk.Label(dialog, text="Nastavenia multimodálnej fúzie",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)

        tk.Label(dialog, text="Váhy pre jednotlivé detektory:",
                font=('Arial', 12), bg='#f3f4f6').pack(pady=5)

        # Frame pre váhy
        weights_frame = tk.Frame(dialog, bg='#f3f4f6')
        weights_frame.pack(fill=tk.X, padx=20, pady=10)

        # Slovník pre uloženie premenných
        weight_vars = {}

        # Vytvorenie sliderov pre každú váhu
        for key, label in [
            ('building', 'Centrálne budovy'),
            ('shape', 'Pravidelné tvary'),
            ('texture', 'Uniformná textúra'),
            ('shadow', 'Detekcia tieňov'),
            ('edge', 'Hrany a kontúry')
        ]:
            frame = tk.Frame(weights_frame, bg='#f3f4f6')
            frame.pack(fill=tk.X, pady=3)

            tk.Label(frame, text=f"{label}:", font=('Arial', 10), bg='#f3f4f6', width=20, anchor='w').pack(side=tk.LEFT)

            var = tk.DoubleVar(value=self.fusion_weights[key])
            weight_vars[key] = var

            scale = tk.Scale(frame, from_=0.0, to=1.0, resolution=0.05, orient=tk.HORIZONTAL,
                           variable=var, bg='#f3f4f6', length=200)
            scale.pack(side=tk.LEFT, padx=10)

            value_label = tk.Label(frame, text=f"{self.fusion_weights[key]:.2f}",
                                 font=('Arial', 9), bg='#f3f4f6', width=5)
            value_label.pack(side=tk.LEFT)

            # Aktualizácia labelu pri zmene
            scale.configure(command=lambda val, lbl=value_label: lbl.config(text=f"{float(val):.2f}"))

        # Prah fúzie
        threshold_frame = tk.Frame(dialog, bg='#f3f4f6')
        threshold_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(threshold_frame, text="Prah fúzie:", font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(anchor='w')

        threshold_var = tk.DoubleVar(value=self.fusion_threshold)
        threshold_scale = tk.Scale(threshold_frame, from_=0.1, to=0.9, resolution=0.05,
                                 orient=tk.HORIZONTAL, variable=threshold_var,
                                 bg='#f3f4f6', length=300)
        threshold_scale.pack(pady=5)

        def apply_settings():
            # Aktualizácia váh
            for key, var in weight_vars.items():
                self.fusion_weights[key] = var.get()

            # Aktualizácia prahu
            self.fusion_threshold = threshold_var.get()

            # Normalizácia váh aby súčet bol 1.0
            total_weight = sum(self.fusion_weights.values())
            if total_weight > 0:
                for key in self.fusion_weights:
                    self.fusion_weights[key] /= total_weight

            dialog.destroy()
            messagebox.showinfo("Úspech", "Nastavenia fúzie boli aktualizované!")

        def reset_defaults():
            # Reset na predvolené hodnoty
            defaults = {'building': 0.3, 'shape': 0.25, 'texture': 0.2, 'shadow': 0.15, 'edge': 0.1}
            for key, var in weight_vars.items():
                var.set(defaults[key])
            threshold_var.set(0.5)

        # Tlačidlá
        buttons_frame = tk.Frame(dialog, bg='#f3f4f6')
        buttons_frame.pack(fill=tk.X, padx=20, pady=15)

        tk.Button(buttons_frame, text="Aplikovať", command=apply_settings,
                 bg='#059669', fg='white', font=('Arial', 11, 'bold'),
                 padx=15, pady=5).pack(side=tk.LEFT, padx=(0, 5))

        tk.Button(buttons_frame, text="Reset", command=reset_defaults,
                 bg='#eab308', fg='white', font=('Arial', 10),
                 padx=15, pady=5).pack(side=tk.LEFT, padx=(0, 5))

        tk.Button(buttons_frame, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 10),
                 padx=15, pady=5).pack(side=tk.RIGHT)

    def start_smart_workflow(self):
        """Spustí inteligentný workflow: roadmap → identifikácia budovy → satellite → detailná analýza."""
        address = self.address_var.get().strip()
        if not address or address == "Zadajte adresu (napr. Bratislava, Slovensko)":
            messagebox.showwarning("Upozornenie", "Prosím, zadajte adresu pre Smart Workflow.")
            return

        if not self.api_key:
            messagebox.showerror("Chyba", "Google Maps API kľúč nie je nakonfigurovaný!")
            self.configure_api_key()
            return

        self.workflow_active = True
        self.building_identified = False
        self.status_var.set("🎯 Spúšťam Smart Workflow - Fáza 1: Roadmap")

        # Najprv načítame roadmap pre identifikáciu budovy
        threading.Thread(target=self.workflow_phase1_roadmap, args=(address,), daemon=True).start()

    def workflow_phase1_roadmap(self, address):
        """Fáza 1: Načítanie roadmap pre identifikáciu budovy."""
        try:
            # Geokódovanie adresy
            if address not in self.geocoding_cache:
                base_url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {'address': address, 'key': self.api_key}
                response = requests.get(base_url, params=params, timeout=10)
                data = response.json()

                if data['status'] == 'OK' and data['results']:
                    location = data['results'][0]['geometry']['location']
                    lat, lng = location['lat'], location['lng']
                    self.geocoding_cache[address] = (lat, lng)
                else:
                    self.root.after(0, lambda: self.show_error(f"Geokódovanie zlyhalo: {data.get('status', 'Neznáma chyba')}"))
                    return
            else:
                lat, lng = self.geocoding_cache[address]

            self.current_lat, self.current_lng = lat, lng
            self.calculate_meters_per_pixel(lat, self.current_zoom)

            # Načítanie roadmap s vyšším zoom pre lepšiu identifikáciu
            roadmap_zoom = min(self.current_zoom + 1, 21)  # Vyšší zoom pre detaily
            base_url = "https://maps.googleapis.com/maps/api/staticmap"
            params = {
                'center': f"{lat},{lng}",
                'zoom': roadmap_zoom,
                'size': f"{self.canvas_width}x{self.canvas_height}",
                'maptype': 'roadmap',
                'key': self.api_key,
                'format': 'png'
            }

            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()
            roadmap_img = Image.open(io.BytesIO(response.content))

            self.roadmap_image = roadmap_img
            self.root.after(0, lambda: self.display_roadmap_for_identification(roadmap_img))

        except Exception as e:
            error_msg = f"Chyba vo fáze 1 (roadmap): {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def display_roadmap_for_identification(self, roadmap_img):
        """Zobrazí roadmap pre identifikáciu cieľovej budovy."""
        try:
            self.current_image = roadmap_img
            self.photo_image = ImageTk.PhotoImage(roadmap_img)
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)

            # Pridanie krížika v strede pre označenie cieľovej lokácie
            center_x, center_y = self.canvas_width // 2, self.canvas_height // 2
            self.canvas.create_line(center_x-20, center_y, center_x+20, center_y,
                                  fill='red', width=3, tags="target_marker")
            self.canvas.create_line(center_x, center_y-20, center_x, center_y+20,
                                  fill='red', width=3, tags="target_marker")
            self.canvas.create_oval(center_x-5, center_y-5, center_x+5, center_y+5,
                                  fill='red', outline='darkred', width=2, tags="target_marker")

            self.status_var.set("🎯 Roadmap načítaná - Kliknite na cieľovú budovu alebo pokračujte")

            # Automatická detekcia budovy v centre
            threading.Thread(target=self.auto_identify_building, args=(roadmap_img,), daemon=True).start()

            # Zobrazenie možností pokračovania
            self.show_workflow_options()

        except Exception as e:
            self.show_error(f"Chyba pri zobrazovaní roadmap: {str(e)}")

    def show_workflow_options(self):
        """Zobrazí možnosti pokračovania vo workflow."""
        # Vytvorenie floating panelu s možnosťami
        options_frame = tk.Frame(self.root, bg='#ffffff', relief=tk.RAISED, bd=2)
        options_frame.place(x=50, y=150, width=300, height=120)

        tk.Label(options_frame, text="🎯 Smart Workflow - Fáza 1",
                font=('Arial', 12, 'bold'), bg='#ffffff').pack(pady=5)

        tk.Label(options_frame, text="Identifikujte cieľovú budovu:",
                font=('Arial', 10), bg='#ffffff').pack(pady=2)

        buttons_frame = tk.Frame(options_frame, bg='#ffffff')
        buttons_frame.pack(pady=5)

        tk.Button(buttons_frame, text="✓ Pokračovať", command=self.workflow_phase2_satellite,
                 bg='#059669', fg='white', font=('Arial', 10, 'bold'),
                 padx=15, pady=3).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="🔄 Znovu", command=self.retry_building_identification,
                 bg='#eab308', fg='white', font=('Arial', 10),
                 padx=15, pady=3).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="❌ Zrušiť", command=self.cancel_workflow,
                 bg='#dc2626', fg='white', font=('Arial', 10),
                 padx=15, pady=3).pack(side=tk.LEFT, padx=5)

        # Uloženie referencie pre neskoršie odstránenie
        self.workflow_options_frame = options_frame

    def auto_identify_building(self, roadmap_img):
        """Automatická identifikácia budovy v roadmap."""
        try:
            if not OPENCV_AVAILABLE:
                return

            # Konverzia na OpenCV formát
            img_array = np.array(roadmap_img)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # Detekcia budov v roadmap (iný prístup ako pre satellite)
            building_bounds = self.detect_building_in_roadmap(img_cv)

            if building_bounds:
                self.target_building_bounds = building_bounds
                self.building_identified = True
                self.root.after(0, lambda: self.highlight_identified_building(building_bounds))

        except Exception as e:
            print(f"Chyba pri automatickej identifikácii budovy: {e}")

    def detect_building_in_roadmap(self, img_cv):
        """Detekuje budovu v roadmap obrázku a vráti jej presné ohraničenie."""
        try:
            h, w = img_cv.shape[:2]
            center_x, center_y = w // 2, h // 2

            # Rozšírený ROI pre lepšiu detekciu budovy
            roi_size = min(w, h) // 3  # Väčší ROI
            x1 = max(0, center_x - roi_size)
            y1 = max(0, center_y - roi_size)
            x2 = min(w, center_x + roi_size)
            y2 = min(h, center_y + roi_size)

            # Pokročilá detekcia budovy v roadmap
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # 1. Detekcia svetlých oblastí (budovy v roadmap)
            _, thresh1 = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

            # 2. Adaptívny prah pre lepšiu detekciu
            thresh2 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, 11, 2)

            # 3. Kombinácia oboch metód
            combined_mask = cv2.bitwise_or(thresh1, thresh2)

            # 4. Morfologické operácie pre vyčistenie
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

            # 5. Nájdenie kontúr v ROI
            roi_mask = combined_mask[y1:y2, x1:x2]
            contours, _ = cv2.findContours(roi_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Nájdenie najväčšej kontúry blízko centra
                best_contour = None
                best_score = 0

                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 200:  # Minimálna veľkosť budovy
                        # Výpočet vzdialenosti od centra ROI
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])

                            # Vzdialenosť od centra ROI
                            roi_center_x = roi_size
                            roi_center_y = roi_size
                            distance = np.sqrt((cx - roi_center_x)**2 + (cy - roi_center_y)**2)

                            # Skóre: väčšia plocha + bližšie k centru = lepšie
                            score = area / (1 + distance)

                            if score > best_score:
                                best_score = score
                                best_contour = contour

                if best_contour is not None:
                    # Prekonvertovanie späť na celý obrázok
                    adjusted_contour = best_contour + [x1, y1]
                    x, y, w_rect, h_rect = cv2.boundingRect(adjusted_contour)

                    # Rozšírenie ohraničenia o 10% pre lepšie pokrytie
                    margin_x = int(w_rect * 0.1)
                    margin_y = int(h_rect * 0.1)

                    final_x1 = max(0, x - margin_x)
                    final_y1 = max(0, y - margin_y)
                    final_x2 = min(w, x + w_rect + margin_x)
                    final_y2 = min(h, y + h_rect + margin_y)

                    print(f"Budova detekovaná: ({final_x1}, {final_y1}, {final_x2}, {final_y2})")
                    return (final_x1, final_y1, final_x2, final_y2)

            # Fallback - centrálna oblasť s 10% okrajom
            fallback_size = min(w, h) // 4
            fallback_x1 = max(0, center_x - fallback_size)
            fallback_y1 = max(0, center_y - fallback_size)
            fallback_x2 = min(w, center_x + fallback_size)
            fallback_y2 = min(h, center_y + fallback_size)

            print(f"Fallback ROI: ({fallback_x1}, {fallback_y1}, {fallback_x2}, {fallback_y2})")
            return (fallback_x1, fallback_y1, fallback_x2, fallback_y2)

        except Exception as e:
            print(f"Chyba v detect_building_in_roadmap: {e}")
            return None

    def highlight_identified_building(self, bounds):
        """Zvýrazní identifikovanú budovu."""
        x1, y1, x2, y2 = bounds
        self.canvas.create_rectangle(x1, y1, x2, y2, outline='lime', width=3, tags="building_highlight")
        self.canvas.create_text((x1 + x2) // 2, y1 - 10, text="🏠 Cieľová budova",
                               fill='lime', font=('Arial', 10, 'bold'), tags="building_highlight")

    def workflow_phase2_satellite(self):
        """Fáza 2: Prepnutie na satellite a detailná analýza strechy."""
        try:
            # Odstránenie options panelu
            if hasattr(self, 'workflow_options_frame'):
                self.workflow_options_frame.destroy()

            self.status_var.set("🎯 Fáza 2: Načítavam satellite pohľad...")

            # Prepnutie na satellite
            self.map_type = "satellite"
            self.map_type_var.set("satellite")

            # Načítanie satellite obrázku
            threading.Thread(target=self.load_satellite_for_analysis, daemon=True).start()

        except Exception as e:
            self.show_error(f"Chyba vo fáze 2: {str(e)}")

    def load_satellite_for_analysis(self):
        """Načíta satellite obrázok pre detailnú analýzu."""
        try:
            base_url = "https://maps.googleapis.com/maps/api/staticmap"
            params = {
                'center': f"{self.current_lat},{self.current_lng}",
                'zoom': self.current_zoom,
                'size': f"{self.canvas_width}x{self.canvas_height}",
                'maptype': 'satellite',
                'key': self.api_key,
                'format': 'png'
            }

            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()
            satellite_img = Image.open(io.BytesIO(response.content))

            self.root.after(0, lambda: self.start_detailed_roof_analysis(satellite_img))

        except Exception as e:
            error_msg = f"Chyba pri načítavaní satellite: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def start_detailed_roof_analysis(self, satellite_img):
        """Spustí detailnú analýzu strechy na satellite obrázku."""
        try:
            self.current_image = satellite_img
            self.photo_image = ImageTk.PhotoImage(satellite_img)
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)

            self.status_var.set("🎯 Fáza 3: Detailná analýza prvkov strechy...")

            # Spustenie pokročilej detekcie prvkov strechy
            threading.Thread(target=self.detect_roof_features, args=(satellite_img,), daemon=True).start()

        except Exception as e:
            self.show_error(f"Chyba pri spustení analýzy: {str(e)}")

    def retry_building_identification(self):
        """Znovu spustí identifikáciu budovy."""
        if hasattr(self, 'workflow_options_frame'):
            self.workflow_options_frame.destroy()
        self.canvas.delete("building_highlight")
        self.auto_identify_building(self.roadmap_image)

    def cancel_workflow(self):
        """Zruší smart workflow."""
        if hasattr(self, 'workflow_options_frame'):
            self.workflow_options_frame.destroy()
        self.workflow_active = False
        self.building_identified = False
        self.canvas.delete("target_marker", "building_highlight")
        self.status_var.set("Smart Workflow zrušený")

    def handle_search(self):
        address = self.address_var.get().strip()
        if not address or address == "Zadajte adresu (napr. Bratislava, Slovensko)":
            messagebox.showwarning("Upozornenie", "Prosím, zadajte adresu.")
            return

        if not self.api_key:
            messagebox.showerror("Chyba", "Google Maps API kľúč nie je nakonfigurovaný!")
            self.configure_api_key()
            return

        self.status_var.set("Vyhľadávam adresu...")
        threading.Thread(target=self.geocode_address, args=(address,), daemon=True).start()

    def geocode_address(self, address: str):
        try:
            if address in self.geocoding_cache:
                lat, lng = self.geocoding_cache[address]
                self.root.after(0, lambda: self.load_satellite_image_from_coords(lat, lng))
                return

            base_url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {'address': address, 'key': self.api_key}
            response = requests.get(base_url, params=params, timeout=10)
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                lat, lng = location['lat'], location['lng']
                self.geocoding_cache[address] = (lat, lng)
                self.root.after(0, lambda: self.load_satellite_image_from_coords(lat, lng))
            else:
                error_msg = f"Geokódovanie zlyhalo: {data.get('status', 'Neznáma chyba')}"
                self.root.after(0, lambda: self.show_error(error_msg))
        except requests.RequestException as e:
            error_msg = f"Chyba siete: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))
        except Exception as e:
            error_msg = f"Neočakávaná chyba: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def load_satellite_image_from_coords(self, lat: float, lng: float):
        try:
            self.status_var.set("Načítavam satelitnú mapu...")
            self.current_lat = lat
            self.current_lng = lng
            self.calculate_meters_per_pixel(lat, self.current_zoom)
            base_url = "https://maps.googleapis.com/maps/api/staticmap"
            params = {
                'center': f"{lat},{lng}", 'zoom': self.current_zoom,
                'size': f"{self.canvas_width}x{self.canvas_height}",
                'maptype': self.map_type, 'key': self.api_key, 'format': 'png'
            }
            threading.Thread(target=self.download_map_image, args=(base_url, params), daemon=True).start()
        except Exception as e:
            self.show_error(f"Chyba pri načítavaní mapy: {str(e)}")

    def download_map_image(self, base_url: str, params: dict):
        try:
            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()
            img = Image.open(io.BytesIO(response.content))
            self.root.after(0, lambda: self.display_map_image(img))
        except requests.RequestException as e:
            error_msg = f"Chyba pri sťahovaní mapy: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))
        except Exception as e:
            error_msg = f"Chyba pri spracovaní obrázka: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def display_map_image(self, img: Image.Image):
        try:
            self.current_image = img
            self.photo_image = ImageTk.PhotoImage(img)
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            self.auto_analyze_btn.config(state=tk.NORMAL)
            self.handle_reset()
            self.status_var.set(f"Mapa načítaná - Zoom: {self.current_zoom}, Typ: {self.map_type}")
        except Exception as e:
            self.show_error(f"Chyba pri zobrazovaní mapy: {str(e)}")

    def calculate_meters_per_pixel(self, lat: float, zoom: int):
        earth_circumference = 40075016.686
        lat_rad = math.radians(lat)
        self.meters_per_pixel = (math.cos(lat_rad) * earth_circumference) / (256 * (2 ** zoom))

    def show_error(self, message: str):
        messagebox.showerror("Chyba", message)
        self.status_var.set("Chyba")

    def perform_roof_detection(self):
        try:
            img_array = np.array(self.current_image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            contours_building = self.detect_central_buildings(img_cv)
            contours_shape = self.detect_rectangular_shapes(img_cv)
            contours_texture = self.detect_by_texture_uniformity(img_cv)
            contours_shadow = self.detect_buildings_with_shadows(img_cv)

            if contours_building is None: contours_building = []
            if contours_shape is None: contours_shape = []
            if contours_texture is None: contours_texture = []
            if contours_shadow is None: contours_shadow = []

            all_contours = list(contours_building) + list(contours_shape) + list(contours_texture) + list(contours_shadow)
            filtered_contours = self.filter_building_contours(all_contours, img_cv.shape)
            self.root.after(0, lambda: self.display_detected_contours(filtered_contours, img_cv))
        except Exception as e:
            error_msg = f"Chyba pri detekcii striech: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def detect_central_buildings(self, img_cv):
        try:
            h, w = img_cv.shape[:2]
            center_x, center_y = w // 2, h // 2
            roi_w, roi_h = int(w * 0.6), int(h * 0.6)
            x1, y1 = center_x - roi_w // 2, center_y - roi_h // 2
            x2, y2 = x1 + roi_w, y1 + roi_h
            roi = img_cv[y1:y2, x1:x2]
            lab_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2LAB)
            l_channel = lab_roi[:, :, 0]
            adaptive_thresh = cv2.adaptiveThreshold(l_channel, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                   cv2.THRESH_BINARY, 11, 2)
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
            cleaned = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
            contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            adjusted_contours = [contour + [x1, y1] for contour in contours]
            return adjusted_contours
        except Exception as e:
            print(f"Chyba v detect_central_buildings: {e}")
            return []

    def detect_rectangular_shapes(self, img_cv):
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)
            edges1 = cv2.Canny(filtered, 30, 100)
            edges2 = cv2.Canny(filtered, 50, 150)
            edges3 = cv2.Canny(filtered, 70, 200)
            combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)
            contours, _ = cv2.findContours(combined_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            rectangular_contours = []
            for contour in contours:
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if 4 <= len(approx) <= 8:
                    area = cv2.contourArea(contour)
                    if area > 1000:
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = float(w) / h
                        if 0.3 <= aspect_ratio <= 3.0:
                            rectangular_contours.append(approx)
            return rectangular_contours
        except Exception as e:
            print(f"Chyba v detect_rectangular_shapes: {e}")
            return []

    def detect_by_texture_uniformity(self, img_cv):
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            kernel_size = 15
            kernel_filter = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size) # Renamed
            mean = cv2.filter2D(gray.astype(np.float32), -1, kernel_filter)
            sqr_mean = cv2.filter2D((gray.astype(np.float32))**2, -1, kernel_filter)
            variance = sqr_mean - mean**2
            std_dev = np.sqrt(np.maximum(variance, 0)) # Ensure variance is non-negative
            uniform_mask = (std_dev < 15).astype(np.uint8) * 255
            kernel_morph = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7)) # Renamed
            uniform_mask = cv2.morphologyEx(uniform_mask, cv2.MORPH_OPEN, kernel_morph)
            uniform_mask = cv2.morphologyEx(uniform_mask, cv2.MORPH_CLOSE, kernel_morph)
            contours, _ = cv2.findContours(uniform_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            return contours
        except Exception as e:
            print(f"Chyba v detect_by_texture_uniformity: {e}")
            return []

    def detect_buildings_with_shadows(self, img_cv):
        try:
            hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
            v_channel = hsv[:, :, 2]
            shadow_mask = (v_channel < 100).astype(np.uint8) * 255
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
            shadow_mask = cv2.morphologyEx(shadow_mask, cv2.MORPH_OPEN, kernel)
            dilated_shadows = cv2.dilate(shadow_mask, kernel, iterations=3)
            non_shadow_mask = cv2.bitwise_not(shadow_mask)
            building_candidates = cv2.bitwise_and(non_shadow_mask, dilated_shadows)
            bright_areas = (v_channel > 120).astype(np.uint8) * 255
            building_mask = cv2.bitwise_and(building_candidates, bright_areas)
            building_mask = cv2.morphologyEx(building_mask, cv2.MORPH_CLOSE, kernel)
            contours, _ = cv2.findContours(building_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            return contours
        except Exception as e:
            print(f"Chyba v detect_buildings_with_shadows: {e}")
            return []

    def detect_roofs_superpixel(self, img_cv):
        """Detekuje strechy pomocou superpixelovej segmentácie a RAG."""
        if not SKIMAGE_AVAILABLE:
            self.root.after(0, lambda: messagebox.showerror("Chyba", "Balíček scikit-image nie je nainštalovaný. Táto funkcia nie je dostupná."))
            return []

        try:
            self.status_var.set("Vykonávam superpixelovú segmentáciu...")
            self.root.update_idletasks()

            image_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)

            # --- 1) Superpixelová segmentácia ---
            segments = segmentation.slic(image_rgb, n_segments=300, compactness=20, sigma=1,
                                         start_label=1, channel_axis=-1 if image_rgb.ndim == 3 else None)

            # --- 2) Graf susednosti regiónov (RAG) ---
            g = graph.rag_mean_color(image_rgb, segments)

            # --- 3) Rozdelenie grafu na zlúčenie podobných regiónov ---
            labels2 = graph.cut_threshold(segments, g, thresh=35)

            # --- 4) Konverzia labelov na masku - identifikácia najväčšieho segmentu ---
            if labels2.size == 0:
                 self.status_var.set("Superpixelová segmentácia: Nenašli sa žiadne segmenty.")
                 return []

            unique_labels, counts = np.unique(labels2.flatten(), return_counts=True)
            if counts.size == 0:
                self.status_var.set("Superpixelová segmentácia: Nepodarilo sa identifikovať hlavný segment.")
                return []

            main_segment_label = unique_labels[counts.argmax()]
            mask = (labels2 == main_segment_label).astype(np.uint8) * 255

            # --- 5) Morfologické vyčistenie ---
            kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7,7))
            kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_close, iterations=2)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel_open, iterations=1)

            # --- 6) Nájdenie kontúr ---
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            self.status_var.set("Superpixelová segmentácia dokončená.")
            return contours

        except Exception as e:
            error_msg = f"Chyba pri superpixelovej segmentácii: {str(e)}"
            print(f"Detailná chyba (superpixel): {e}")
            self.root.after(0, lambda: self.show_error(error_msg))
            return []

    def filter_building_contours(self, contours, img_shape):
        h, w = img_shape[:2]
        center_x, center_y = w // 2, h // 2
        min_area = (w * h) * 0.005
        max_area = (w * h) * 0.25
        scored_contours = []

        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area or area > max_area:
                continue
            score = self.calculate_building_score(contour, center_x, center_y, w, h)
            if score > 0.3:
                epsilon = 0.015 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) >= 3:
                    scored_contours.append((approx, score))

        scored_contours.sort(key=lambda x: x[1], reverse=True)
        final_contours = []
        for contour, score in scored_contours:
            is_duplicate = False
            for existing_contour in final_contours:
                if self.contours_overlap(contour, existing_contour, img_shape):
                    is_duplicate = True
                    break
            if not is_duplicate:
                final_contours.append(contour)
            if len(final_contours) >= 5:
                break
        return final_contours

    def calculate_building_score(self, contour, center_x, center_y, img_w, img_h):
        score = 0.0
        area = cv2.contourArea(contour)
        x, y, w, h = cv2.boundingRect(contour)
        contour_center_x, contour_center_y = x + w // 2, y + h // 2
        distance_from_center = np.sqrt((contour_center_x - center_x)**2 + (contour_center_y - center_y)**2)
        max_distance = np.sqrt(center_x**2 + center_y**2)
        centrality_score = 1.0 - (distance_from_center / max_distance if max_distance > 0 else 0) # Avoid division by zero
        score += centrality_score * 0.3
        aspect_ratio = float(w) / h if h > 0 else 0 # Avoid division by zero
        if 0.5 <= aspect_ratio <= 2.0: score += 0.25
        elif 0.3 <= aspect_ratio <= 3.0: score += 0.15
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        solidity = float(area) / hull_area if hull_area > 0 else 0
        if solidity > 0.8: score += 0.2
        elif solidity > 0.6: score += 0.1
        rect_area = w * h
        extent = float(area) / rect_area if rect_area > 0 else 0
        if extent > 0.7: score += 0.15
        elif extent > 0.5: score += 0.1
        relative_area = area / (img_w * img_h) if (img_w * img_h) > 0 else 0 # Avoid division by zero
        if 0.01 <= relative_area <= 0.15: score += 0.1
        elif 0.005 <= relative_area <= 0.2: score += 0.05
        return score

    def contours_overlap(self, contour1, contour2, img_shape):
        mask1 = np.zeros(img_shape[:2], dtype=np.uint8)
        mask2 = np.zeros(img_shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask1, [contour1], 255)
        cv2.fillPoly(mask2, [contour2], 255)
        intersection = cv2.bitwise_and(mask1, mask2)
        intersection_area = cv2.countNonZero(intersection)
        area1 = cv2.contourArea(contour1)
        area2 = cv2.contourArea(contour2)
        min_area = min(area1, area2)
        overlap_ratio = intersection_area / min_area if min_area > 0 else 0
        return overlap_ratio > 0.4

    def display_detected_contours(self, contours, img_cv):
        self.detected_contours = contours
        try:
            h, w = img_cv.shape[:2]
            overlay_arr = np.zeros((h, w, 4), dtype=np.uint8) # Renamed
            colors = [
                (0, 255, 0, 180), (255, 0, 0, 180), (0, 0, 255, 180),
                (255, 255, 0, 180), (255, 0, 255, 180), (0, 255, 255, 180),
                (128, 0, 128, 180), (255, 165, 0, 180), (255, 192, 203, 180),
                (0, 128, 0, 180)
            ]
            for i, contour in enumerate(contours):
                color_val = colors[i % len(colors)] # Renamed
                temp_overlay = np.zeros((h, w, 3), dtype=np.uint8)
                cv2.drawContours(temp_overlay, [contour], -1, color_val[:3], 2)
                cv2.fillPoly(temp_overlay, [contour], color_val[:3])
                mask = np.any(temp_overlay > 0, axis=2)
                overlay_arr[mask] = [color_val[0], color_val[1], color_val[2], color_val[3]]
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    cv2.putText(overlay_arr, str(i+1), (cx-10, cy+5), cv2.FONT_HERSHEY_SIMPLEX, 0.8,
                               (255, 255, 255, 255), 2)
            self.contour_overlay = Image.fromarray(overlay_arr, 'RGBA')
            self.redraw_canvas()
            self.status_var.set(f"Detekcia dokončená - nájdených {len(contours)} striech")
            messagebox.showinfo("Detekcia striech", f"Nájdených {len(contours)} potenciálnych striech.\n\nKliknite na kontúru pre výber alebo použite 'Zobraziť kontúry' pre detailnú analýzu.")
        except Exception as e:
            self.status_var.set("Chyba pri zobrazovaní kontúr")
            messagebox.showerror("Chyba", f"Chyba pri zobrazovaní kontúr: {str(e)}")
            print(f"Detailná chyba: {e}")

    def handle_canvas_click(self, event):
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv vyhľadajte adresu.")
            return
        x, y = event.x, event.y
        self.points.append(Point(x, y))
        self.redraw_canvas()
        self.update_area()

    def redraw_canvas(self):
        self.canvas.delete("all")
        if self.photo_image:
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)

        if self.show_contours.get() and self.contour_overlay:
            if self.current_image and self.contour_overlay:
                try:
                    current_img_rgba = self.current_image.convert('RGBA') # Renamed
                    contour_img_rgba = self.contour_overlay.convert('RGBA') # Renamed
                    if current_img_rgba.size != contour_img_rgba.size:
                        contour_img_rgba = contour_img_rgba.resize(current_img_rgba.size, Image.Resampling.LANCZOS)
                    blended = Image.alpha_composite(current_img_rgba, contour_img_rgba)
                    blended_rgb = blended.convert('RGB') # Renamed
                    self.contour_photo = ImageTk.PhotoImage(blended_rgb)
                    self.canvas.create_image(0, 0, anchor=tk.NW, image=self.contour_photo)
                except Exception as e:
                    print(f"Chyba pri blendovaní: {e}")
                    contour_photo_fallback = ImageTk.PhotoImage(self.contour_overlay) # Renamed
                    self.canvas.create_image(0, 0, anchor=tk.NW, image=contour_photo_fallback)

        if self.show_edges.get() and hasattr(self, 'edge_overlay') and self.edge_overlay:
            try:
                edge_photo = ImageTk.PhotoImage(self.edge_overlay)
                self.canvas.create_image(0, 0, anchor=tk.NW, image=edge_photo)
            except Exception as e:
                print(f"Chyba pri zobrazovaní hrán: {e}")

        if self.show_contours.get() and self.detected_contours:
            self.draw_contour_boundaries()

        if len(self.points) > 0:
            if len(self.points) > 1:
                for i in range(len(self.points)):
                    p1, p2 = self.points[i], self.points[(i + 1) % len(self.points)]
                    if i == len(self.points) - 1 and len(self.points) < 3: break
                    self.canvas.create_line(p1.x, p1.y, p2.x, p2.y, fill='#00FF00', width=3, capstyle=tk.ROUND)
                    if len(self.points) >= 3 or i < len(self.points) - 1:
                        self.draw_side_length(p1, p2)
            if len(self.points) >= 3:
                coords = [val for point in self.points for val in (point.x, point.y)]
                self.canvas.create_polygon(coords, fill='#00FF00', outline='#00FF00', width=3, stipple='gray25')
            for point in self.points:
                self.canvas.create_oval(point.x - 5, point.y - 5, point.x + 5, point.y + 5,
                                       fill='#00FF00', outline='#008000', width=2)

    def draw_contour_boundaries(self):
        colors = ['#FF0000', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
                 '#800080', '#FFA500', '#FFC0CB', '#008000', '#800000']
        for i, contour in enumerate(self.detected_contours):
            color_val = colors[i % len(colors)] # Renamed
            points_coords = [val for point in contour for val in point[0]] # Renamed
            if len(points_coords) >= 6:
                self.canvas.create_polygon(points_coords, fill=color_val, outline=color_val, width=2,
                                         stipple='gray25', tags=f"contour_{i}")
                self.canvas.create_polygon(points_coords, fill='', outline=color_val, width=3, tags=f"contour_{i}_outline")
                self.canvas.tag_bind(f"contour_{i}", "<Button-1>", lambda e, idx=i: self.select_contour(idx))
                self.canvas.tag_bind(f"contour_{i}_outline", "<Button-1>", lambda e, idx=i: self.select_contour(idx))
                if len(contour) > 0:
                    cx = sum(point[0][0] for point in contour) / len(contour)
                    cy = sum(point[0][1] for point in contour) / len(contour)
                    self.canvas.create_oval(cx-15, cy-15, cx+15, cy+15,
                                          fill='white', outline=color_val, width=2, tags=f"contour_{i}_bg")
                    self.canvas.create_text(cx, cy, text=str(i+1), fill=color_val,
                                          font=('Arial', 12, 'bold'), tags=f"contour_{i}_text")
                    self.canvas.tag_bind(f"contour_{i}_bg", "<Button-1>", lambda e, idx=i: self.select_contour(idx))
                    self.canvas.tag_bind(f"contour_{i}_text", "<Button-1>", lambda e, idx=i: self.select_contour(idx))

    def select_contour(self, contour_index):
        if contour_index < len(self.detected_contours):
            contour = self.detected_contours[contour_index]
            self.points = [Point(point[0][0], point[0][1]) for point in contour]
            self.redraw_canvas()
            self.update_area()
            self.status_var.set(f"Vybratá kontúra {contour_index + 1}")
            messagebox.showinfo("Kontúra vybratá", f"Kontúra {contour_index + 1} bola vybratá ako obrys strechy.")

    def draw_side_length(self, p1: Point, p2: Point):
        dx, dy = p2.x - p1.x, p2.y - p1.y
        pixel_distance = math.sqrt(dx*dx + dy*dy)
        real_distance = pixel_distance * self.meters_per_pixel
        if real_distance < 1: return
        mid_x, mid_y = p1.x + dx / 2, p1.y + dy / 2
        text = f"{real_distance:.1f} m"
        self.canvas.create_rectangle(mid_x - 25, mid_y - 8, mid_x + 25, mid_y + 8, fill='black', outline='black')
        self.canvas.create_text(mid_x, mid_y, text=text, fill='white', font=('Arial', 10, 'bold'))

    def calculate_polygon_area(self, vertices: List[Point]) -> float:
        if len(vertices) < 3: return 0
        area = 0
        j = len(vertices) - 1
        for i in range(len(vertices)):
            area += (vertices[j].x + vertices[i].x) * (vertices[j].y - vertices[i].y)
            j = i
        return abs(area / 2)

    def update_area(self):
        self.undo_btn.config(state=tk.NORMAL if len(self.points) > 0 else tk.DISABLED)
        self.reset_btn.config(state=tk.NORMAL if len(self.points) > 0 else tk.DISABLED)
        if len(self.points) < 3:
            self.area_label.config(text="0.00 m²")
            return
        pixel_area = self.calculate_polygon_area(self.points)
        real_area = pixel_area * (self.meters_per_pixel ** 2)
        self.area_label.config(text=f"{real_area:.2f} m²")

    def handle_reset(self):
        self.points = []
        self.redraw_canvas()
        self.update_area()

    def handle_undo(self):
        if self.points:
            self.points.pop()
            self.redraw_canvas()
            self.update_area()

    def handle_auto_analyze(self):
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv načítajte mapu.")
            return
        self.show_analysis_options()

    def show_analysis_options(self):
        dialog = tk.Toplevel(self.root)
        dialog.title("Automatická analýza strechy")
        dialog.geometry("400x350") # Adjusted height for new option
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        tk.Label(dialog, text="Automatická analýza strechy",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)
        tk.Label(dialog, text="Vyberte typ analýzy:",
                font=('Arial', 12), bg='#f3f4f6').pack(pady=5) # Reduced pady

        analysis_var = tk.StringVar(value="building_detection")

        tk.Radiobutton(dialog, text="🏠 Detekcia budov (odporúčané)",
                      variable=analysis_var, value="building_detection",
                      bg='#f3f4f6', font=('Arial', 10, 'bold')).pack(pady=3)
        tk.Radiobutton(dialog, text="📐 Detekcia pravidelných tvarov",
                      variable=analysis_var, value="shape_detection",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=3)
        tk.Radiobutton(dialog, text="🎨 Detekcia jednotnej textúry",
                      variable=analysis_var, value="texture_detection",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=3)
        tk.Radiobutton(dialog, text="🌑 Detekcia tieňov a budov",
                      variable=analysis_var, value="shadow_detection",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=3)
        tk.Radiobutton(dialog, text="📊 Superpixelová segmentácia", # Nová možnosť
                      variable=analysis_var, value="superpixel_segmentation",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=3)
        tk.Radiobutton(dialog, text="🔍 Pokročilá kombinovaná analýza",
                      variable=analysis_var, value="advanced_combined",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=3)
        tk.Radiobutton(dialog, text="🧠 Multimodálna fúzia (AI)",
                      variable=analysis_var, value="multimodal_fusion",
                      bg='#f3f4f6', font=('Arial', 10, 'bold'), fg='#7c3aed').pack(pady=3)

        tk.Label(dialog, text="Citlivosť detekcie:",
                font=('Arial', 11), bg='#f3f4f6').pack(pady=(10, 2)) # Adjusted pady
        sensitivity_var = tk.DoubleVar(value=0.5)
        sensitivity_scale = tk.Scale(dialog, from_=0.1, to=1.0, resolution=0.1,
                                   orient=tk.HORIZONTAL, variable=sensitivity_var,
                                   bg='#f3f4f6', length=200)
        sensitivity_scale.pack(pady=(0,5)) # Adjusted pady

        def start_analysis():
            analysis_type = analysis_var.get()
            sensitivity = sensitivity_var.get()
            dialog.destroy()
            self.perform_auto_analysis(analysis_type, sensitivity)

        tk.Button(dialog, text="Spustiť analýzu", command=start_analysis,
                 bg='#059669', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=5).pack(pady=10) # Adjusted pady
        tk.Button(dialog, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 11),
                 padx=20, pady=5).pack(pady=5)

    def perform_auto_analysis(self, analysis_type: str, sensitivity: float):
        self.auto_analyze_btn.config(state=tk.DISABLED, text="Analyzujem...")
        self.status_var.set("Spúšťam automatickú analýzu...")
        threading.Thread(target=self.run_analysis, args=(analysis_type, sensitivity), daemon=True).start()

    def run_analysis(self, analysis_type: str, sensitivity: float):
        try:
            img_array = np.array(self.current_image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            contours = []

            if analysis_type == "building_detection":
                contours = self.detect_central_buildings(img_cv)
            elif analysis_type == "shape_detection":
                contours = self.detect_rectangular_shapes(img_cv)
            elif analysis_type == "texture_detection":
                contours = self.detect_by_texture_uniformity(img_cv)
            elif analysis_type == "shadow_detection":
                contours = self.detect_buildings_with_shadows(img_cv)
            elif analysis_type == "superpixel_segmentation":
                if not SKIMAGE_AVAILABLE:
                    self.root.after(0, lambda: messagebox.showerror("Chyba", "Balíček scikit-image nie je nainštalovaný. Superpixelová segmentácia nie je dostupná."))
                    self.root.after(0, lambda: self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza"))
                    self.root.after(0, lambda: self.status_var.set("Analýza zlyhala: scikit-image chýba"))
                    return
                contours = self.detect_roofs_superpixel(img_cv)
            elif analysis_type == "multimodal_fusion":
                contours = self.multimodal_roof_detection(img_cv, sensitivity)
            else:  # advanced_combined
                contours_building = self.detect_central_buildings(img_cv)
                contours_shape = self.detect_rectangular_shapes(img_cv)
                contours_texture = self.detect_by_texture_uniformity(img_cv)
                contours_shadow = self.detect_buildings_with_shadows(img_cv)

                if contours_building is None: contours_building = []
                if contours_shape is None: contours_shape = []
                if contours_texture is None: contours_texture = []
                if contours_shadow is None: contours_shadow = []

                all_contours_combined = list(contours_building) + list(contours_shape) + list(contours_texture) + list(contours_shadow)
                contours = self.filter_building_contours(all_contours_combined, img_cv.shape)

            if analysis_type not in ["advanced_combined", "superpixel_segmentation"]:
                if contours:
                    contours = self.filter_building_contours(contours, img_cv.shape)

            self.root.after(0, lambda: self.display_detected_contours(contours, img_cv))
            self.root.after(0, lambda: self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza"))
            self.root.after(0, lambda: self.status_var.set("Automatická analýza dokončená."))


        except Exception as e:
            error_msg = f"Chyba pri automatickej analýze ({analysis_type}): {str(e)}"
            print(f"Detailná chyba (run_analysis): {e}")
            self.root.after(0, lambda: self.show_error(error_msg))
            self.root.after(0, lambda: self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza"))
            self.root.after(0, lambda: self.status_var.set(f"Analýza ({analysis_type}) zlyhala."))


    def complete_auto_analysis(self, detected_points): # This method seems less used now with direct contour display
        if detected_points and len(detected_points) >= 3:
            self.points = detected_points
            self.redraw_canvas()
            self.update_area()
            self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza")
            self.status_var.set(f"Analýza dokončená - nájdených {len(detected_points)} bodov")
            messagebox.showinfo("Úspech", f"Automatická analýza dokončená!\nNájdených {len(detected_points)} bodov strechy.")
        else:
            self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza")
            self.status_var.set("Analýza neúspešná")
            messagebox.showwarning("Upozornenie", "Automatická analýza nenašla žiadne vhodné body strechy.\nSkúste manuálne označenie alebo zmeňte nastavenia.")

    def export_results(self):
        if not self.points or len(self.points) < 3:
            messagebox.showwarning("Upozornenie", "Nie sú k dispozícii žiadne výsledky na export.")
            return
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")],
            title="Exportovať výsledky"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("Analyzátor rozmerov strechy - Výsledky\n" + "=" * 50 + "\n\n")
                    f.write(f"Dátum: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}\n")
                    f.write(f"Adresa: {self.address_var.get()}\n")
                    f.write(f"Súradnice: {self.current_lat:.6f}, {self.current_lng:.6f}\n")
                    f.write(f"Zoom: {self.current_zoom}\nTyp mapy: {self.map_type}\n")
                    f.write(f"Metrový pomer: {self.meters_per_pixel:.6f} m/pixel\n\n")
                    pixel_area = self.calculate_polygon_area(self.points)
                    real_area = pixel_area * (self.meters_per_pixel ** 2)
                    f.write(f"Plocha strechy: {real_area:.2f} m²\nPočet bodov: {len(self.points)}\n\n")
                    f.write("Body polygónu (pixel súradnice):\n")
                    for i, point in enumerate(self.points, 1):
                        f.write(f"{i:2d}. X: {point.x:6.1f}, Y: {point.y:6.1f}\n")
                    f.write("\nDĺžky strán:\n")
                    for i in range(len(self.points)):
                        p1, p2 = self.points[i], self.points[(i + 1) % len(self.points)]
                        dx, dy = p2.x - p1.x, p2.y - p1.y
                        pixel_distance = math.sqrt(dx*dx + dy*dy)
                        real_distance = pixel_distance * self.meters_per_pixel
                        f.write(f"Strana {i+1}-{((i+1) % len(self.points))+1}: {real_distance:.2f} m\n")
                messagebox.showinfo("Úspech", f"Výsledky boli exportované do súboru:\n{filename}")
            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri exporte: {str(e)}")

    def save_project(self):
        if not self.points:
            messagebox.showwarning("Upozornenie", "Nie je čo uložiť.")
            return
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Uložiť projekt"
        )
        if filename:
            try:
                project_data = {
                    'version': '1.0', 'timestamp': datetime.now().isoformat(),
                    'address': self.address_var.get(),
                    'coordinates': {'lat': self.current_lat, 'lng': self.current_lng},
                    'map_settings': {'zoom': self.current_zoom, 'map_type': self.map_type},
                    'points': [{'x': p.x, 'y': p.y} for p in self.points],
                    'meters_per_pixel': self.meters_per_pixel
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Úspech", f"Projekt bol uložený do súboru:\n{filename}")
            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri ukladaní: {str(e)}")

    def load_project(self):
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Načítať projekt"
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)
                if 'address' in project_data: self.address_var.set(project_data['address'])
                if 'coordinates' in project_data:
                    coords = project_data['coordinates']
                    self.current_lat, self.current_lng = coords['lat'], coords['lng']
                if 'map_settings' in project_data:
                    settings = project_data['map_settings']
                    self.current_zoom = settings.get('zoom', 20)
                    self.map_type = settings.get('map_type', 'satellite')
                    self.zoom_var.set(self.current_zoom)
                    self.map_type_var.set(self.map_type)
                if 'meters_per_pixel' in project_data: self.meters_per_pixel = project_data['meters_per_pixel']
                if 'points' in project_data: self.points = [Point(p['x'], p['y']) for p in project_data['points']]
                if self.current_lat and self.current_lng:
                    self.load_satellite_image_from_coords(self.current_lat, self.current_lng)
                messagebox.showinfo("Úspech", f"Projekt bol načítaný zo súboru:\n{filename}")
            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri načítavaní: {str(e)}")

    def show_about(self):
        dialog = tk.Toplevel(self.root)
        dialog.title("O aplikácii")
        dialog.geometry("400x350") # Adjusted height
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        tk.Label(dialog, text="Analyzátor rozmerov strechy",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=15) # Adjusted pady
        tk.Label(dialog, text="Verzia 4.0 (Smart Workflow + AI)", # Updated version
                font=('Arial', 12), bg='#f3f4f6').pack(pady=5)
        tk.Label(dialog, text="Profesionálny nástroj na analýzu rozmerov striech\npoužívajúci Google Maps API",
                font=('Arial', 10), bg='#f3f4f6', justify=tk.CENTER).pack(pady=10)
        tk.Label(dialog, text="Funkcie:",
                font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(pady=(10, 5)) # Adjusted pady
        features = [
            "• 🎯 Smart Workflow: roadmap → satellite",
            "• 🏠 Automatická identifikácia budovy",
            "• 📐 Detekcia narožia a hrebeňa strechy",
            "• 🪟 Rozpoznávanie strešných okien",
            "• 🏭 Detekcia komínov a prvkov",
            "• 🧠 Multimodálna AI fúzia algoritmov",
            "• 📊 Presné meranie a export výsledkov"
        ]
        for feature in features:
            tk.Label(dialog, text=feature, font=('Arial', 9), bg='#f3f4f6', anchor='w').pack(pady=1, padx=20, fill='x') # Added padx and fill
        tk.Button(dialog, text="Zavrieť", command=dialog.destroy,
                 bg='#2563eb', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5).pack(pady=15) # Adjusted pady

    def create_contour_analysis_window(self):
        analysis_window = tk.Toplevel(self.root)
        analysis_window.title("Analýza kontúr striech")
        analysis_window.geometry("800x600")
        analysis_window.configure(bg='#f3f4f6')
        notebook = ttk.Notebook(analysis_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        visual_frame = ttk.Frame(notebook)
        notebook.add(visual_frame, text="Vizuálna analýza")
        if MATPLOTLIB_AVAILABLE:
            self.create_visual_analysis_tab(visual_frame)
        else:
            tk.Label(visual_frame, text="matplotlib nie je nainštalované.\nNainštalujte ho pre vizuálnu analýzu.",
                    font=('Arial', 12), bg='#f3f4f6').pack(pady=50)
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Štatistická analýza")
        self.create_statistical_analysis_tab(stats_frame)

    def create_visual_analysis_tab(self, parent):
        try:
            fig, axes = plt.subplots(2, 2, figsize=(10, 8))
            fig.suptitle('Analýza detekcie striech', fontsize=14)
            img_array = np.array(self.current_image)
            axes[0, 0].imshow(img_array)
            axes[0, 0].set_title('Originálny obrázok'); axes[0, 0].axis('off')

            if OPENCV_AVAILABLE:
                try:
                    if len(img_array.shape) == 3:
                        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY if img_array.shape[2] == 3 else cv2.COLOR_RGBA2GRAY)
                    elif len(img_array.shape) == 2: gray = img_array
                    else: raise ValueError("Nepodporovaný formát obrázka")
                    edges = cv2.Canny(gray, 50, 150)
                    axes[0, 1].imshow(edges, cmap='gray')
                    axes[0, 1].set_title('Detekcia hrán'); axes[0, 1].axis('off')
                except Exception as e:
                    axes[0, 1].text(0.5, 0.5, f'Chyba detekcie hrán:\n{str(e)}', transform=axes[0, 1].transAxes, ha='center', va='center', fontsize=8, wrap=True)
                    axes[0, 1].set_title('Detekcia hrán - chyba')
            else:
                axes[0, 1].text(0.5, 0.5, 'OpenCV nie je dostupné', transform=axes[0, 1].transAxes, ha='center', va='center')
                axes[0, 1].set_title('Detekcia hrán - nedostupné')

            if self.contour_overlay:
                try:
                    contour_array = np.array(self.contour_overlay)
                    axes[1, 0].imshow(contour_array)
                    axes[1, 0].set_title('Detekované kontúry'); axes[1, 0].axis('off')
                except Exception as e:
                    axes[1, 0].text(0.5, 0.5, f'Chyba kontúr: {str(e)}', transform=axes[1, 0].transAxes, ha='center', va='center')
                    axes[1, 0].set_title('Detekované kontúry - chyba')

            if self.contour_overlay and self.current_image:
                try:
                    current_img_rgba = self.current_image.convert('RGBA')
                    contour_img_rgba = self.contour_overlay.convert('RGBA')
                    if current_img_rgba.size != contour_img_rgba.size:
                        contour_img_rgba = contour_img_rgba.resize(current_img_rgba.size, Image.Resampling.LANCZOS)
                    blended = Image.alpha_composite(current_img_rgba, contour_img_rgba).convert('RGB')
                    axes[1, 1].imshow(np.array(blended))
                    axes[1, 1].set_title('Kombinovaný pohľad'); axes[1, 1].axis('off')
                except Exception as e:
                    axes[1, 1].text(0.5, 0.5, f'Chyba blendovania: {str(e)}', transform=axes[1, 1].transAxes, ha='center', va='center')
                    axes[1, 1].set_title('Kombinovaný pohľad - chyba')
            else:
                axes[1, 1].text(0.5, 0.5, 'Žiadne kontúry na zobrazenie', transform=axes[1, 1].transAxes, ha='center', va='center')
                axes[1, 1].set_title('Kombinovaný pohľad')

            plt.tight_layout(rect=[0, 0, 1, 0.96]) # Adjust layout to prevent title overlap
            canvas_widget = FigureCanvasTkAgg(fig, parent)
            canvas_widget.draw()
            canvas_widget.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        except Exception as e:
            tk.Label(parent, text=f"Chyba pri vytváraní vizualizácie: {str(e)}",
                    font=('Arial', 10), bg='#f3f4f6', fg='red').pack(pady=20)

    def create_statistical_analysis_tab(self, parent):
        canvas_stats = tk.Canvas(parent, bg='#f3f4f6') # Renamed
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas_stats.yview)
        scrollable_frame = ttk.Frame(canvas_stats)
        scrollable_frame.bind("<Configure>", lambda e: canvas_stats.configure(scrollregion=canvas_stats.bbox("all")))
        canvas_stats.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas_stats.configure(yscrollcommand=scrollbar.set)

        tk.Label(scrollable_frame, text="Štatistická analýza detekovaných striech",
                font=('Arial', 14, 'bold')).pack(pady=10)
        if self.detected_contours:
            tk.Label(scrollable_frame, text=f"Celkový počet detekovaných kontúr: {len(self.detected_contours)}",
                    font=('Arial', 12)).pack(pady=5)
            for i, contour in enumerate(self.detected_contours):
                frame = tk.LabelFrame(scrollable_frame, text=f"Kontúra {i+1}",
                                     font=('Arial', 11, 'bold'), padx=10, pady=5)
                frame.pack(fill=tk.X, padx=10, pady=5)
                if OPENCV_AVAILABLE:
                    area_pixels = cv2.contourArea(contour)
                    area_meters = area_pixels * (self.meters_per_pixel ** 2)
                    perimeter_pixels = cv2.arcLength(contour, True)
                    perimeter_meters = perimeter_pixels * self.meters_per_pixel
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h if h > 0 else 0
                    hull = cv2.convexHull(contour)
                    hull_area = cv2.contourArea(hull)
                    solidity = float(area_pixels) / hull_area if hull_area > 0 else 0
                    tk.Label(frame, text=f"Plocha: {area_meters:.2f} m² ({area_pixels:.0f} px)", font=('Arial', 10)).pack(anchor='w') # Added px
                    tk.Label(frame, text=f"Obvod: {perimeter_meters:.2f} m ({perimeter_pixels:.0f} px)", font=('Arial', 10)).pack(anchor='w') # Added px
                    tk.Label(frame, text=f"Pomer strán: {aspect_ratio:.2f}", font=('Arial', 10)).pack(anchor='w')
                    tk.Label(frame, text=f"Kompaktnosť: {solidity:.2f}", font=('Arial', 10)).pack(anchor='w')
                    tk.Label(frame, text=f"Počet bodov: {len(contour)}", font=('Arial', 10)).pack(anchor='w')
                    select_btn = tk.Button(frame, text="Vybrať túto kontúru",
                                          command=lambda idx=i: self.select_contour(idx),
                                          bg='#2563eb', fg='white', font=('Arial', 9))
                    select_btn.pack(pady=5)
        else:
            tk.Label(scrollable_frame, text="Žiadne kontúry na analýzu.",
                    font=('Arial', 12), fg='gray').pack(pady=20)
        canvas_stats.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def run_custom_detection(self):
        try:
            img_array = np.array(self.current_image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            contours_building = self.detect_central_buildings(img_cv)
            contours_shape = self.detect_rectangular_shapes(img_cv)
            contours_texture = self.detect_by_texture_uniformity(img_cv)
            contours_shadow = self.detect_buildings_with_shadows(img_cv)

            if contours_building is None: contours_building = []
            if contours_shape is None: contours_shape = []
            if contours_texture is None: contours_texture = []
            if contours_shadow is None: contours_shadow = []

            all_contours = list(contours_building) + list(contours_shape) + list(contours_texture) + list(contours_shadow)
            filtered_contours = self.filter_building_contours_custom(all_contours, img_cv.shape)
            self.root.after(0, lambda: self.display_detected_contours(filtered_contours, img_cv))
        except Exception as e:
            error_msg = f"Chyba pri vlastnej detekcii: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def filter_building_contours_custom(self, contours, img_shape):
        h, w = img_shape[:2]
        center_x, center_y = w // 2, h // 2
        min_area_ratio = getattr(self, 'custom_min_area', 0.005)
        max_area_ratio = getattr(self, 'custom_max_area', 0.25)
        score_threshold = getattr(self, 'custom_threshold', 0.3)
        min_area = (w * h) * min_area_ratio
        max_area = (w * h) * max_area_ratio
        scored_contours = []

        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area or area > max_area: continue
            score = self.calculate_building_score(contour, center_x, center_y, w, h)
            if score > score_threshold:
                epsilon = 0.015 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) >= 3:
                    scored_contours.append((approx, score))

        scored_contours.sort(key=lambda x: x[1], reverse=True)
        final_contours = []
        for contour, score in scored_contours:
            is_duplicate = False
            for existing_contour in final_contours:
                if self.contours_overlap(contour, existing_contour, img_shape):
                    is_duplicate = True
                    break
            if not is_duplicate:
                final_contours.append(contour)
            if len(final_contours) >= 5: break
        return final_contours

    def multimodal_roof_detection(self, img_cv, sensitivity=0.5):
        """
        Multimodálna detekcia striech kombinujúca všetky metódy cez váženú fúziu skóre.

        Args:
            img_cv: OpenCV obrázok
            sensitivity: Citlivosť detekcie (0.1-1.0)

        Returns:
            List kontúr detekovaných striech
        """
        try:
            self.status_var.set("Spúšťam multimodálnu fúziu...")
            h, w = img_cv.shape[:2]

            # Vytvorenie pixel-level skóre máp pre každú metódu
            building_score_map = self.create_building_score_map(img_cv)
            shape_score_map = self.create_shape_score_map(img_cv)
            texture_score_map = self.create_texture_score_map(img_cv)
            shadow_score_map = self.create_shadow_score_map(img_cv)
            edge_score_map = self.create_edge_score_map(img_cv)

            # Fúzia skóre máp pomocou vážených súm
            fused_score_map = self.fuse_score_maps(
                building_score_map, shape_score_map, texture_score_map,
                shadow_score_map, edge_score_map
            )

            # Adaptívny prah na základe citlivosti
            adaptive_threshold = self.calculate_adaptive_threshold(fused_score_map, sensitivity)

            # Vytvorenie finálnej masky
            roof_mask = (fused_score_map > adaptive_threshold).astype(np.uint8) * 255

            # Morfologické vyčistenie
            roof_mask = self.clean_roof_mask(roof_mask)

            # Extrakcia kontúr
            contours = self.extract_final_contours(roof_mask, img_cv.shape)

            self.status_var.set(f"Multimodálna fúzia dokončená - {len(contours)} striech")
            return contours

        except Exception as e:
            error_msg = f"Chyba pri multimodálnej fúzii: {str(e)}"
            print(f"Detailná chyba (multimodal): {e}")
            self.root.after(0, lambda: self.show_error(error_msg))
            return []

    def create_building_score_map(self, img_cv):
        """Vytvorí skóre mapu pre centrálne budovy."""
        try:
            h, w = img_cv.shape[:2]
            score_map = np.zeros((h, w), dtype=np.float32)

            # Zameranie na centrálnu oblasť
            center_x, center_y = w // 2, h // 2
            roi_w, roi_h = int(w * 0.6), int(h * 0.6)
            x1, y1 = center_x - roi_w // 2, center_y - roi_h // 2
            x2, y2 = x1 + roi_w, y1 + roi_h

            # Konverzia do LAB priestoru
            lab_img = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
            l_channel = lab_img[:, :, 0].astype(np.float32)

            # Lokálna variancia pre detekciu uniformných oblastí
            kernel = np.ones((15, 15), np.float32) / 225
            mean = cv2.filter2D(l_channel, -1, kernel)
            sqr_mean = cv2.filter2D(l_channel**2, -1, kernel)
            variance = sqr_mean - mean**2

            # Nízka variancia = vyššie skóre
            uniformity_score = 1.0 / (1.0 + variance / 100.0)

            # Centrálne skóre - vyššie v strede
            y_coords, x_coords = np.ogrid[:h, :w]
            center_distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
            max_distance = np.sqrt(center_x**2 + center_y**2)
            centrality_score = 1.0 - (center_distance / max_distance)

            # Kombinované skóre
            score_map = uniformity_score * centrality_score

            return score_map

        except Exception as e:
            print(f"Chyba v create_building_score_map: {e}")
            return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.float32)

    def create_shape_score_map(self, img_cv):
        """Vytvorí skóre mapu pre pravidelné tvary."""
        try:
            h, w = img_cv.shape[:2]
            score_map = np.zeros((h, w), dtype=np.float32)

            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Detekcia hrán s viacerými prahmi
            edges1 = cv2.Canny(gray, 30, 100)
            edges2 = cv2.Canny(gray, 50, 150)
            edges3 = cv2.Canny(gray, 70, 200)

            # Kombinované hrany
            combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))

            # Houghove čiary pre detekciu pravidelných tvarov
            lines = cv2.HoughLines(combined_edges, 1, np.pi/180, threshold=50)

            if lines is not None:
                # Vytvorenie mapy hustoty čiar
                line_density = np.zeros((h, w), dtype=np.float32)

                for line in lines:
                    rho, theta = line[0]
                    a = np.cos(theta)
                    b = np.sin(theta)
                    x0 = a * rho
                    y0 = b * rho

                    # Kreslenie čiary s váhou
                    x1 = int(x0 + 1000 * (-b))
                    y1 = int(y0 + 1000 * (a))
                    x2 = int(x0 - 1000 * (-b))
                    y2 = int(y0 - 1000 * (a))

                    cv2.line(line_density, (x1, y1), (x2, y2), 1.0, 2)

                # Gaussovské rozmazanie pre hladšie skóre
                score_map = cv2.GaussianBlur(line_density, (15, 15), 5)

                # Normalizácia
                if score_map.max() > 0:
                    score_map = score_map / score_map.max()

            return score_map

        except Exception as e:
            print(f"Chyba v create_shape_score_map: {e}")
            return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.float32)

    def create_texture_score_map(self, img_cv):
        """Vytvorí skóre mapu pre uniformnú textúru."""
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Lokálna štandardná odchýlka pre uniformnosť textúry
            kernel_size = 15
            kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
            mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
            sqr_mean = cv2.filter2D((gray.astype(np.float32))**2, -1, kernel)
            variance = sqr_mean - mean**2
            std_dev = np.sqrt(np.maximum(variance, 0))

            # Nízka štandardná odchýlka = uniformná textúra = vyššie skóre
            texture_score = 1.0 / (1.0 + std_dev / 20.0)

            return texture_score
        except Exception as e:
            print(f"Chyba v create_texture_score_map: {e}")
            return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.float32)

    def create_shadow_score_map(self, img_cv):
        """Vytvorí skóre mapu na základe tieňov."""
        try:
            hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
            v_channel = hsv[:, :, 2].astype(np.float32)

            # Detekcia tieňov (tmavé oblasti)
            shadow_mask = (v_channel < 100).astype(np.float32)

            # Dilatácia tieňov pre nájdenie susedných oblastí
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 15))
            dilated_shadows = cv2.dilate(shadow_mask, kernel, iterations=2)

            # Svetlé oblasti susediace s tieňmi = potenciálne budovy
            bright_areas = (v_channel > 120).astype(np.float32) / 255.0
            shadow_score = dilated_shadows * bright_areas * (1.0 - shadow_mask)

            return shadow_score
        except Exception as e:
            print(f"Chyba v create_shadow_score_map: {e}")
            return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.float32)

    def create_edge_score_map(self, img_cv):
        """Vytvorí skóre mapu na základe hrán."""
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Detekcia hrán
            edges = cv2.Canny(gray, 50, 150)

            # Rozmazanie hrán pre hladšie skóre
            edge_score = cv2.GaussianBlur(edges.astype(np.float32), (11, 11), 3) / 255.0

            return edge_score
        except Exception as e:
            print(f"Chyba v create_edge_score_map: {e}")
            return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.float32)

    def fuse_score_maps(self, building_map, shape_map, texture_map, shadow_map, edge_map):
        """Fúzia všetkých skóre máp pomocou vážených súm."""
        try:
            # Normalizácia všetkých máp na rozsah 0-1
            maps = [building_map, shape_map, texture_map, shadow_map, edge_map]
            normalized_maps = []

            for score_map in maps:
                if score_map.max() > 0:
                    normalized_map = score_map / score_map.max()
                else:
                    normalized_map = score_map
                normalized_maps.append(normalized_map)

            # Vážená suma
            fused_map = (
                self.fusion_weights['building'] * normalized_maps[0] +
                self.fusion_weights['shape'] * normalized_maps[1] +
                self.fusion_weights['texture'] * normalized_maps[2] +
                self.fusion_weights['shadow'] * normalized_maps[3] +
                self.fusion_weights['edge'] * normalized_maps[4]
            )

            return fused_map
        except Exception as e:
            print(f"Chyba v fuse_score_maps: {e}")
            return np.zeros_like(building_map)

    def calculate_adaptive_threshold(self, score_map, sensitivity):
        """Vypočíta adaptívny prah na základe citlivosti."""
        try:
            # Základný prah
            base_threshold = self.fusion_threshold

            # Adaptácia na základe citlivosti
            # Vyššia citlivosť = nižší prah = viac detekovaných oblastí
            adaptive_threshold = base_threshold * (1.1 - sensitivity)

            # Štatistická adaptácia na základe distribúcie skóre
            mean_score = np.mean(score_map)
            std_score = np.std(score_map)

            # Ak je distribúcia veľmi uniformná, znížime prah
            if std_score < 0.1:
                adaptive_threshold *= 0.8

            return max(0.1, min(0.9, adaptive_threshold))
        except Exception as e:
            print(f"Chyba v calculate_adaptive_threshold: {e}")
            return 0.5

    def clean_roof_mask(self, mask):
        """Vyčistí masku pomocou morfologických operácií."""
        try:
            # Odstránenie šumu
            kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            cleaned = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel_open)

            # Zatvorenie dier
            kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_close)

            # Vyhladzenie
            cleaned = cv2.medianBlur(cleaned, 5)

            return cleaned
        except Exception as e:
            print(f"Chyba v clean_roof_mask: {e}")
            return mask

    def extract_final_contours(self, mask, img_shape):
        """Extrahuje finálne kontúry z vyčistenej masky."""
        try:
            # Nájdenie kontúr
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return []

            # Filtrovanie kontúr
            h, w = img_shape[:2]
            min_area = (w * h) * 0.005  # Minimálne 0.5% plochy obrazu
            max_area = (w * h) * 0.3    # Maximálne 30% plochy obrazu

            filtered_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    # Zjednodušenie kontúry
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)

                    if len(approx) >= 3:  # Minimálne trojuholník
                        filtered_contours.append(approx)

            # Zoradenie podľa plochy (najväčšie prvé)
            filtered_contours.sort(key=lambda c: cv2.contourArea(c), reverse=True)

            # Odstránenie prekrývajúcich sa kontúr
            final_contours = []
            for contour in filtered_contours:
                is_duplicate = False
                for existing in final_contours:
                    if self.contours_overlap(contour, existing, img_shape):
                        is_duplicate = True
                        break

                if not is_duplicate:
                    final_contours.append(contour)

                # Limit na 5 najlepších kontúr
                if len(final_contours) >= 5:
                    break

            return final_contours

        except Exception as e:
            print(f"Chyba v extract_final_contours: {e}")
            return []

    def detect_roof_features(self, satellite_img):
        """Detekuje špecifické prvky strechy len v oblasti identifikovanej budovy +10%."""
        try:
            img_array = np.array(satellite_img)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # Určenie ROI na základe identifikovanej budovy
            roi_bounds = self.calculate_focused_roi(img_cv.shape)

            # Detekcia kontúr a hraníc strechy v ROI
            roof_contours = self.detect_roof_contours_in_roi(img_cv, roi_bounds)

            # Detekcia špecifických prvkov len v ROI
            corners = self.detect_roof_corners_focused(img_cv, roi_bounds)
            ridge = self.detect_roof_ridge_focused(img_cv, roi_bounds)
            windows = self.detect_roof_windows_focused(img_cv, roi_bounds)
            chimney = self.detect_chimney_focused(img_cv, roi_bounds)

            # Zobrazenie výsledkov
            self.root.after(0, lambda: self.display_focused_roof_analysis(
                roof_contours, corners, ridge, windows, chimney, roi_bounds))

        except Exception as e:
            error_msg = f"Chyba pri detekcii prvkov strechy: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def calculate_focused_roi(self, img_shape):
        """Vypočíta ROI na základe identifikovanej budovy +10% okraj."""
        h, w = img_shape[:2]

        if self.target_building_bounds and self.building_identified:
            # Použiť identifikované hranice budovy
            x1, y1, x2, y2 = self.target_building_bounds

            # Pridať 10% okraj
            width = x2 - x1
            height = y2 - y1
            margin_x = int(width * 0.1)
            margin_y = int(height * 0.1)

            # Rozšíriť ROI s okrajom
            roi_x1 = max(0, x1 - margin_x)
            roi_y1 = max(0, y1 - margin_y)
            roi_x2 = min(w, x2 + margin_x)
            roi_y2 = min(h, y2 + margin_y)
        else:
            # Fallback - centrálna oblasť 40% obrazu
            center_x, center_y = w // 2, h // 2
            roi_size_x = int(w * 0.2)  # 40% šírka
            roi_size_y = int(h * 0.2)  # 40% výška

            roi_x1 = max(0, center_x - roi_size_x)
            roi_y1 = max(0, center_y - roi_size_y)
            roi_x2 = min(w, center_x + roi_size_x)
            roi_y2 = min(h, center_y + roi_size_y)

        return (roi_x1, roi_y1, roi_x2, roi_y2)

    def detect_roof_contours_in_roi(self, img_cv, roi_bounds):
        """Detekuje hlavné kontúry a hranice strechy v ROI."""
        try:
            roi_x1, roi_y1, roi_x2, roi_y2 = roi_bounds

            # Extrahovanie ROI
            roi = img_cv[roi_y1:roi_y2, roi_x1:roi_x2]

            if roi.size == 0:
                return []

            # Multimodálna detekcia v ROI
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 1. Detekcia hrán s viacerými prahmi
            edges1 = cv2.Canny(gray_roi, 30, 100)
            edges2 = cv2.Canny(gray_roi, 50, 150)
            edges3 = cv2.Canny(gray_roi, 70, 200)
            combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))

            # 2. Morfologické spracovanie pre spojenie hrán
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)

            # 3. Detekcia kontúr
            contours, _ = cv2.findContours(combined_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 4. Filtrovanie kontúr pre strechy
            roof_contours = []
            roi_area = (roi_x2 - roi_x1) * (roi_y2 - roi_y1)
            min_area = roi_area * 0.05  # Min 5% ROI
            max_area = roi_area * 0.8   # Max 80% ROI

            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    # Zjednodušenie kontúry
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)

                    if len(approx) >= 4:  # Minimálne 4 body pre strechu
                        # Prekonvertovanie späť na celý obrázok
                        global_contour = approx + [roi_x1, roi_y1]
                        roof_contours.append(global_contour)

            # Zoradenie podľa plochy
            roof_contours.sort(key=lambda c: cv2.contourArea(c), reverse=True)

            return roof_contours[:3]  # Max 3 najväčšie kontúry

        except Exception as e:
            print(f"Chyba v detect_roof_contours_in_roi: {e}")
            return []

    def detect_roof_corners_focused(self, img_cv, roi_bounds):
        """Detekuje narozia strechy v ROI pomocí Harris corner detection."""
        try:
            roi_x1, roi_y1, roi_x2, roi_y2 = roi_bounds

            # Extrahovanie ROI
            roi = img_cv[roi_y1:roi_y2, roi_x1:roi_x2]
            if roi.size == 0:
                return []

            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # Harris corner detection v ROI
            corners = cv2.cornerHarris(gray_roi, 2, 3, 0.04)
            corners = cv2.dilate(corners, None)

            # Prah pre významné narozia
            if corners.max() > 0:
                threshold = 0.01 * corners.max()
                corner_coords = np.where(corners > threshold)

                # Prekonvertovanie na globálne súradnice
                global_corners = []
                for y, x in zip(corner_coords[0], corner_coords[1]):
                    global_x = x + roi_x1
                    global_y = y + roi_y1
                    global_corners.append((global_x, global_y))

                return global_corners[:8]  # Max 8 najvýznamnejších narožia

            return []

        except Exception as e:
            print(f"Chyba v detect_roof_corners_focused: {e}")
            return []

    def detect_roof_corners(self, img_cv):
        """Detekuje narozia strechy pomocí Harris corner detection."""
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Harris corner detection
            corners = cv2.cornerHarris(gray, 2, 3, 0.04)
            corners = cv2.dilate(corners, None)

            # Prah pre významné narozia
            threshold = 0.01 * corners.max()
            corner_coords = np.where(corners > threshold)

            # Filtrovanie narožia v centrálnej oblasti (kde je budova)
            h, w = img_cv.shape[:2]
            center_x, center_y = w // 2, h // 2
            roi_size = min(w, h) // 3

            filtered_corners = []
            for y, x in zip(corner_coords[0], corner_coords[1]):
                if (center_x - roi_size < x < center_x + roi_size and
                    center_y - roi_size < y < center_y + roi_size):
                    filtered_corners.append((x, y))

            return filtered_corners[:8]  # Max 8 najvýznamnejších narožia

        except Exception as e:
            print(f"Chyba v detect_roof_corners: {e}")
            return []

    def detect_roof_ridge_focused(self, img_cv, roi_bounds):
        """Detekuje hrebeň strechy v ROI."""
        try:
            roi_x1, roi_y1, roi_x2, roi_y2 = roi_bounds
            roi = img_cv[roi_y1:roi_y2, roi_x1:roi_x2]

            if roi.size == 0:
                return []

            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray_roi, 50, 150)

            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=30,
                                   minLineLength=20, maxLineGap=8)

            if lines is None:
                return []

            ridge_lines = []
            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi

                if abs(angle) < 30 or abs(angle) > 150:
                    length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                    if length > 25:
                        # Prekonvertovanie na globálne súradnice
                        global_line = ((x1 + roi_x1, y1 + roi_y1),
                                     (x2 + roi_x1, y2 + roi_y1))
                        ridge_lines.append(global_line)

            return ridge_lines[:3]

        except Exception as e:
            print(f"Chyba v detect_roof_ridge_focused: {e}")
            return []

    def detect_roof_windows_focused(self, img_cv, roi_bounds):
        """Detekuje strešné okná v ROI."""
        try:
            roi_x1, roi_y1, roi_x2, roi_y2 = roi_bounds
            roi = img_cv[roi_y1:roi_y2, roi_x1:roi_x2]

            if roi.size == 0:
                return []

            hsv_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

            lower_dark = np.array([0, 0, 0])
            upper_dark = np.array([180, 255, 80])
            dark_mask = cv2.inRange(hsv_roi, lower_dark, upper_dark)

            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            dark_mask = cv2.morphologyEx(dark_mask, cv2.MORPH_OPEN, kernel)

            contours, _ = cv2.findContours(dark_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            windows = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 30 < area < 300:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h

                    if 0.5 < aspect_ratio < 2.0:
                        # Prekonvertovanie na globálne súradnice
                        global_window = (x + roi_x1, y + roi_y1,
                                       x + w + roi_x1, y + h + roi_y1)
                        windows.append(global_window)

            return windows[:5]

        except Exception as e:
            print(f"Chyba v detect_roof_windows_focused: {e}")
            return []

    def detect_chimney_focused(self, img_cv, roi_bounds):
        """Detekuje komín v ROI."""
        try:
            roi_x1, roi_y1, roi_x2, roi_y2 = roi_bounds
            roi = img_cv[roi_y1:roi_y2, roi_x1:roi_x2]

            if roi.size == 0:
                return []

            hsv_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

            lower_dark = np.array([0, 0, 0])
            upper_dark = np.array([180, 255, 100])
            dark_mask = cv2.inRange(hsv_roi, lower_dark, upper_dark)

            kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 7))
            chimney_mask = cv2.morphologyEx(dark_mask, cv2.MORPH_OPEN, kernel_vertical)

            contours, _ = cv2.findContours(chimney_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            chimneys = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 20 < area < 200:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h

                    if 0.3 < aspect_ratio < 1.5:
                        # Prekonvertovanie na globálne súradnice
                        global_chimney = (x + roi_x1, y + roi_y1,
                                        x + w + roi_x1, y + h + roi_y1)
                        chimneys.append(global_chimney)

            return chimneys[:2]

        except Exception as e:
            print(f"Chyba v detect_chimney_focused: {e}")
            return []

    def detect_roof_ridge(self, img_cv):
        """Detekuje hrebeň strechy pomocou detekcie čiar."""
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Detekcia hrán
            edges = cv2.Canny(gray, 50, 150)

            # Houghove čiary pre detekciu hrebeňa
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=30, maxLineGap=10)

            if lines is None:
                return []

            # Filtrovanie horizontálnych a diagonálnych čiar (typické pre hrebeň)
            ridge_lines = []
            for line in lines:
                x1, y1, x2, y2 = line[0]

                # Výpočet uhla čiary
                angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi

                # Hrebeň je typicky horizontálny alebo mierne diagonálny
                if abs(angle) < 30 or abs(angle) > 150:
                    length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                    if length > 40:  # Minimálna dĺžka hrebeňa
                        ridge_lines.append(((x1, y1), (x2, y2)))

            return ridge_lines[:3]  # Max 3 najdlhšie čiary

        except Exception as e:
            print(f"Chyba v detect_roof_ridge: {e}")
            return []

    def detect_roof_windows(self, img_cv):
        """Detekuje strešné okná (svetlíky)."""
        try:
            hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)

            # Maska pre tmavé oblasti (okná sú typicky tmavšie)
            lower_dark = np.array([0, 0, 0])
            upper_dark = np.array([180, 255, 80])
            dark_mask = cv2.inRange(hsv, lower_dark, upper_dark)

            # Morfologické operácie
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            dark_mask = cv2.morphologyEx(dark_mask, cv2.MORPH_OPEN, kernel)
            dark_mask = cv2.morphologyEx(dark_mask, cv2.MORPH_CLOSE, kernel)

            # Nájdenie kontúr
            contours, _ = cv2.findContours(dark_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            windows = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 50 < area < 500:  # Typická veľkosť strešného okna
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h

                    # Okná sú typicky štvorcové alebo obdĺžnikové
                    if 0.5 < aspect_ratio < 2.0:
                        windows.append((x, y, x+w, y+h))

            return windows[:5]  # Max 5 okien

        except Exception as e:
            print(f"Chyba v detect_roof_windows: {e}")
            return []

    def detect_chimney(self, img_cv):
        """Detekuje komín na streche."""
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)

            # Komíny sú typicky tmavé a vertikálne
            # Detekcia tmavých oblastí
            lower_dark = np.array([0, 0, 0])
            upper_dark = np.array([180, 255, 100])
            dark_mask = cv2.inRange(hsv, lower_dark, upper_dark)

            # Morfologické operácie pre komíny
            kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 7))
            chimney_mask = cv2.morphologyEx(dark_mask, cv2.MORPH_OPEN, kernel_vertical)

            # Nájdenie kontúr
            contours, _ = cv2.findContours(chimney_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            chimneys = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 30 < area < 300:  # Typická veľkosť komína z vtáčej perspektívy
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h

                    # Komíny sú typicky vyššie ako široké alebo štvorcové
                    if 0.3 < aspect_ratio < 1.5:
                        chimneys.append((x, y, x+w, y+h))

            return chimneys[:2]  # Max 2 komíny

        except Exception as e:
            print(f"Chyba v detect_chimney: {e}")
            return []

    def display_roof_features(self, corners, ridge, windows, chimney):
        """Zobrazí detekované prvky strechy na canvase."""
        try:
            # Vyčistenie predchádzajúcich označení
            self.canvas.delete("roof_features")

            # Zobrazenie narožia (zelené kruhy)
            for x, y in corners:
                self.canvas.create_oval(x-4, y-4, x+4, y+4,
                                      fill='lime', outline='darkgreen', width=2,
                                      tags="roof_features")
                self.canvas.create_text(x+10, y-10, text="📐",
                                      font=('Arial', 12), tags="roof_features")

            # Zobrazenie hrebeňa (červené čiary)
            for (x1, y1), (x2, y2) in ridge:
                self.canvas.create_line(x1, y1, x2, y2,
                                      fill='red', width=3, tags="roof_features")
                mid_x, mid_y = (x1 + x2) // 2, (y1 + y2) // 2
                self.canvas.create_text(mid_x, mid_y-15, text="🏔️",
                                      font=('Arial', 12), tags="roof_features")

            # Zobrazenie okien (modré obdĺžniky)
            for x1, y1, x2, y2 in windows:
                self.canvas.create_rectangle(x1, y1, x2, y2,
                                           outline='blue', width=2, tags="roof_features")
                self.canvas.create_text((x1+x2)//2, (y1+y2)//2, text="🪟",
                                      font=('Arial', 10), tags="roof_features")

            # Zobrazenie komína (oranžové obdĺžniky)
            for x1, y1, x2, y2 in chimney:
                self.canvas.create_rectangle(x1, y1, x2, y2,
                                           outline='orange', width=3, tags="roof_features")
                self.canvas.create_text((x1+x2)//2, (y1+y2)//2, text="🏭",
                                      font=('Arial', 12), tags="roof_features")

            # Aktualizácia statusu
            feature_count = len(corners) + len(ridge) + len(windows) + len(chimney)
            self.status_var.set(f"🎯 Smart Workflow dokončený - {feature_count} prvkov detekovaných")

            # Zobrazenie legendy
            self.show_feature_legend()

        except Exception as e:
            self.show_error(f"Chyba pri zobrazovaní prvkov: {str(e)}")

    def display_focused_roof_analysis(self, roof_contours, corners, ridge, windows, chimney, roi_bounds):
        """Zobrazí výsledky focused analýzy strechy."""
        try:
            # Vyčistenie predchádzajúcich označení
            self.canvas.delete("roof_features", "roof_contours", "roi_highlight")

            # Zvýraznenie ROI oblasti
            roi_x1, roi_y1, roi_x2, roi_y2 = roi_bounds
            self.canvas.create_rectangle(roi_x1, roi_y1, roi_x2, roi_y2,
                                       outline='yellow', width=2, dash=(5, 5),
                                       tags="roi_highlight")
            self.canvas.create_text(roi_x1 + 5, roi_y1 + 5, text="🎯 ROI",
                                  fill='yellow', font=('Arial', 10, 'bold'),
                                  anchor='nw', tags="roi_highlight")

            # Zobrazenie hlavných kontúr strechy (fialové)
            for i, contour in enumerate(roof_contours):
                points = [val for point in contour for val in point[0]]
                if len(points) >= 6:
                    self.canvas.create_polygon(points, fill='', outline='magenta',
                                             width=3, tags="roof_contours")
                    # Označenie kontúry
                    if len(contour) > 0:
                        cx = sum(point[0][0] for point in contour) / len(contour)
                        cy = sum(point[0][1] for point in contour) / len(contour)
                        self.canvas.create_text(cx, cy, text=f"🏠{i+1}",
                                              fill='magenta', font=('Arial', 12, 'bold'),
                                              tags="roof_contours")

            # Zobrazenie narožia (zelené kruhy)
            for x, y in corners:
                self.canvas.create_oval(x-4, y-4, x+4, y+4,
                                      fill='lime', outline='darkgreen', width=2,
                                      tags="roof_features")
                self.canvas.create_text(x+10, y-10, text="📐",
                                      font=('Arial', 12), tags="roof_features")

            # Zobrazenie hrebeňa (červené čiary)
            for (x1, y1), (x2, y2) in ridge:
                self.canvas.create_line(x1, y1, x2, y2,
                                      fill='red', width=3, tags="roof_features")
                mid_x, mid_y = (x1 + x2) // 2, (y1 + y2) // 2
                self.canvas.create_text(mid_x, mid_y-15, text="🏔️",
                                      font=('Arial', 12), tags="roof_features")

            # Zobrazenie okien (modré obdĺžniky)
            for x1, y1, x2, y2 in windows:
                self.canvas.create_rectangle(x1, y1, x2, y2,
                                           outline='blue', width=2, tags="roof_features")
                self.canvas.create_text((x1+x2)//2, (y1+y2)//2, text="🪟",
                                      font=('Arial', 10), tags="roof_features")

            # Zobrazenie komína (oranžové obdĺžniky)
            for x1, y1, x2, y2 in chimney:
                self.canvas.create_rectangle(x1, y1, x2, y2,
                                           outline='orange', width=3, tags="roof_features")
                self.canvas.create_text((x1+x2)//2, (y1+y2)//2, text="🏭",
                                      font=('Arial', 12), tags="roof_features")

            # Aktualizácia statusu
            total_features = len(roof_contours) + len(corners) + len(ridge) + len(windows) + len(chimney)
            self.status_var.set(f"🎯 Smart Workflow dokončený - {total_features} prvkov v ROI")

            # Zobrazenie rozšírenej legendy
            self.show_focused_legend(len(roof_contours), len(corners), len(ridge), len(windows), len(chimney))

        except Exception as e:
            self.show_error(f"Chyba pri zobrazovaní focused analýzy: {str(e)}")

    def show_focused_legend(self, contours_count, corners_count, ridge_count, windows_count, chimney_count):
        """Zobrazí rozšírenú legendu s počtami prvkov."""
        # Vytvorenie legendy v pravom hornom rohu
        legend_frame = tk.Frame(self.root, bg='#ffffff', relief=tk.RAISED, bd=2)
        legend_frame.place(x=self.root.winfo_width()-280, y=150, width=250, height=180)

        tk.Label(legend_frame, text="🎯 Smart Workflow - Výsledky",
                font=('Arial', 11, 'bold'), bg='#ffffff').pack(pady=5)

        legend_items = [
            (f"🏠 Kontúry strechy ({contours_count})", "magenta"),
            (f"📐 Narozia ({corners_count})", "lime"),
            (f"🏔️ Hrebeň ({ridge_count})", "red"),
            (f"🪟 Okná ({windows_count})", "blue"),
            (f"🏭 Komín ({chimney_count})", "orange"),
            ("🎯 ROI oblasť", "yellow")
        ]

        for text, color in legend_items:
            item_frame = tk.Frame(legend_frame, bg='#ffffff')
            item_frame.pack(fill=tk.X, padx=5, pady=1)

            color_box = tk.Frame(item_frame, bg=color, width=15, height=15)
            color_box.pack(side=tk.LEFT, padx=(0, 5))

            tk.Label(item_frame, text=text, font=('Arial', 9), bg='#ffffff', anchor='w').pack(side=tk.LEFT)

        # Tlačidlo na zatvorenie legendy
        tk.Button(legend_frame, text="✕", command=legend_frame.destroy,
                 bg='#dc2626', fg='white', font=('Arial', 8, 'bold'),
                 width=2, height=1).place(x=225, y=5)

        # Uloženie referencie
        self.focused_legend_frame = legend_frame

    def show_feature_legend(self):
        """Zobrazí legendu detekovaných prvkov."""
        # Vytvorenie legendy v pravom hornom rohu
        legend_frame = tk.Frame(self.root, bg='#ffffff', relief=tk.RAISED, bd=2)
        legend_frame.place(x=self.root.winfo_width()-250, y=150, width=200, height=150)

        tk.Label(legend_frame, text="🎯 Detekované prvky",
                font=('Arial', 11, 'bold'), bg='#ffffff').pack(pady=5)

        legend_items = [
            ("📐 Narozia strechy", "lime"),
            ("🏔️ Hrebeň strechy", "red"),
            ("🪟 Strešné okná", "blue"),
            ("🏭 Komín", "orange")
        ]

        for text, color in legend_items:
            item_frame = tk.Frame(legend_frame, bg='#ffffff')
            item_frame.pack(fill=tk.X, padx=5, pady=1)

            color_box = tk.Frame(item_frame, bg=color, width=15, height=15)
            color_box.pack(side=tk.LEFT, padx=(0, 5))

            tk.Label(item_frame, text=text, font=('Arial', 9), bg='#ffffff', anchor='w').pack(side=tk.LEFT)

        # Tlačidlo na zatvorenie legendy
        tk.Button(legend_frame, text="✕", command=legend_frame.destroy,
                 bg='#dc2626', fg='white', font=('Arial', 8, 'bold'),
                 width=2, height=1).place(x=175, y=5)

        # Uloženie referencie
        self.feature_legend_frame = legend_frame

def main():
    root = tk.Tk()
    app = RoofAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()